import numpy as np
from sklearn.metrics import confusion_matrix, f1_score, precision_score, recall_score, accuracy_score, roc_auc_score
from sklearn.utils.multiclass import unique_labels

def safe_division(a, b, default=0.0):
    """安全的除法运算，避免除零错误"""
    try:
        return a / b if b != 0 else default
    except:
        return default

def get_accuracy(labels, prediction):    
    try:
        cm = confusion_matrix(labels, prediction)
        if cm.size == 0:
            return 0.0
            
        def linear_assignment(cost_matrix):    
            import lap
            try:
                _, x, y = lap.lapjv(cost_matrix, extend_cost=True)
                return np.array([[y[i], i] for i in x if i >= 0])
            except:
                return np.array([])
                
        def _make_cost_m(cm):
            s = np.max(cm)
            return (- cm + s)
            
        indexes = linear_assignment(_make_cost_m(cm))
        if len(indexes) == 0:
            return 0.0
            
        js = [e[1] for e in sorted(indexes, key=lambda x: x[0])]
        cm2 = cm[:, js]    
        accuracy = safe_division(np.trace(cm2), np.sum(cm2))
        return accuracy
    except Exception as e:
        print(f"警告: 计算准确率时出错: {str(e)}")
        return 0.0

def get_accuracy_with_new_label(labels, prediction):    
    try:
        cm = confusion_matrix(labels, prediction)
        if cm.size == 0:
            return 0.0, labels
            
        def linear_assignment(cost_matrix):    
            import lap
            try:
                _, x, y = lap.lapjv(cost_matrix, extend_cost=True)
                return np.array([[y[i], i] for i in x if i >= 0])
            except:
                return np.array([])
                
        def _make_cost_m(cm):
            s = np.max(cm)
            return (- cm + s)
            
        indexes = linear_assignment(_make_cost_m(cm))
        if len(indexes) == 0:
            return 0.0, labels
            
        js = [e[1] for e in sorted(indexes, key=lambda x: x[0])]
        cm2 = cm[:, js]    
        accuracy = safe_division(np.trace(cm2), np.sum(cm2))

        label_dict_in = {}
        label_dict_out = {}
        label_change = {}
        for i,label in enumerate(unique_labels(labels, prediction)):
            label_dict_in[label] = i
            label_dict_out[i] = label
        for i,ind in enumerate(js):
            label_change[i] = ind
        labels_new = [label_dict_out[label_change[label_dict_in[i]]] for i in labels]
        return accuracy, labels_new
    except Exception as e:
        print(f"警告: 计算新标签准确率时出错: {str(e)}")
        return 0.0, labels

def get_scores(labels, predictions):
    try:
        accuracy = get_accuracy(labels, predictions)
        precision, recall, f_score, _ = precision_recall_fscore_support(
            labels, predictions,
            average='binary',
            pos_label=1,
            zero_division=0
        )
        return f_score, precision, recall, accuracy
    except Exception as e:
        print(f"警告: 计算分数时出错: {str(e)}")
        return 0.0, 0.0, 0.0, 0.0

def get_MCM_score(y_true, y_pred):
    """计算多类别混淆矩阵分数"""
    try:
        cm = confusion_matrix(y_true, y_pred)
        n_classes = cm.shape[0]
        
        # 计算每个类别的准确率
        class_acc = np.zeros(n_classes)
        for i in range(n_classes):
            class_acc[i] = cm[i, i] / np.sum(cm[i, :]) if np.sum(cm[i, :]) > 0 else 0
        
        # 计算平均准确率
        mean_acc = np.mean(class_acc)
        
        return mean_acc
    except Exception as e:
        print(f"警告: 计算MCM分数时出错: {str(e)}")
        return 0.0

def calculate_metrics(y_true, y_pred, y_prob=None):
    """计算各种评估指标"""
    try:
        metrics = {
            'accuracy': accuracy_score(y_true, y_pred),
            'precision': precision_score(y_true, y_pred, average='weighted'),
            'recall': recall_score(y_true, y_pred, average='weighted'),
            'f1': f1_score(y_true, y_pred, average='weighted'),
            'mcm_score': get_MCM_score(y_true, y_pred)
        }
        
        # 如果提供了预测概率，计算AUC
        if y_prob is not None and len(y_prob.shape) > 1 and y_prob.shape[1] > 1:
            try:
                metrics['auc'] = roc_auc_score(y_true, y_prob[:, 1])
            except:
                metrics['auc'] = 0.5
        
        return metrics
    except Exception as e:
        print(f"警告: 计算评估指标时出错: {str(e)}")
        return {
            'accuracy': 0.0,
            'precision': 0.0,
            'recall': 0.0,
            'f1': 0.0,
            'mcm_score': 0.0,
            'auc': 0.5
        }

def get_MCM_score_with_new_label(labels, predictions):
    try:
        accuracy, labels_new = get_accuracy_with_new_label(labels, predictions)
        precision, recall, f_score, true_sum, MCM = precision_recall_fscore_support(
            labels_new, predictions,
            average='macro',
            zero_division=0
        )
        
        tn = MCM[:, 0, 0]
        fp = MCM[:, 0, 1]
        fn = MCM[:, 1, 0] 
        tp = MCM[:, 1, 1] 
        
        fpr_array = np.array([safe_division(f, (f + t)) for f, t in zip(fp, tn)])
        fnr_array = np.array([safe_division(f, (t + f)) for f, t in zip(fn, tp)])
        f1_array = np.array([safe_division(2 * t, (2 * t + f + fn)) for t, f, fn in zip(tp, fp, fn)])
        sum_array = fn + tp
        
        M_fpr = np.mean(fpr_array)
        M_fnr = np.mean(fnr_array)
        M_f1 = np.mean(f1_array)
        
        W_fpr = safe_division(np.sum(fpr_array * sum_array), np.sum(sum_array))
        W_fnr = safe_division(np.sum(fnr_array * sum_array), np.sum(sum_array))
        W_f1 = safe_division(np.sum(f1_array * sum_array), np.sum(sum_array))
        
        return {
            "M_fpr": M_fpr,
            "M_fnr": M_fnr,
            "M_f1": M_f1,
            "W_fpr": W_fpr,
            "W_fnr": W_fnr,
            "W_f1": W_f1,
            "ACC": accuracy
        }
    except Exception as e:
        print(f"警告: 计算新标签MCM分数时出错: {str(e)}")
        return {
            "M_fpr": 0.0,
            "M_fnr": 0.0,
            "M_f1": 0.0,
            "W_fpr": 0.0,
            "W_fnr": 0.0,
            "W_f1": 0.0,
            "ACC": 0.0
        }

