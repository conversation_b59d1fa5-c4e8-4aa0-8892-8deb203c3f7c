import torch
import torch.nn as nn
import torch.nn.functional as F
from metrics import get_MCM_score
from sklearn.metrics import f1_score, precision_score, recall_score, accuracy_score, confusion_matrix, roc_auc_score
import numpy as np
from tqdm import tqdm

def calculate_metrics(y_true, y_pred, y_prob):
    """计算各种评估指标"""
    # 计算混淆矩阵
    tn, fp, fn, tp = confusion_matrix(y_true, y_pred).ravel()
    
    # 计算基本指标
    accuracy = (tp + tn) / (tp + tn + fp + fn)
    precision = tp / (tp + fp) if (tp + fp) > 0 else 0
    recall = tp / (tp + fn) if (tp + fn) > 0 else 0
    f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
    
    # 计算FPR和FNR
    fpr = fp / (fp + tn) if (fp + tn) > 0 else 0
    fnr = fn / (fn + tp) if (fn + tp) > 0 else 0
    
    # 计算AUC
    try:
        if y_prob is not None:
            # 确保使用正类的概率
            if len(y_prob.shape) > 1:
                y_prob = y_prob[:, 1]  # 取正类的概率
            # 确保y_true和y_prob长度一致
            if len(y_true) == len(y_prob):
                auc = roc_auc_score(y_true, y_prob)
            else:
                print(f"警告: y_true长度({len(y_true)})与y_prob长度({len(y_prob)})不一致")
                auc = 0.5
        else:
            auc = 0.5
    except Exception as e:
        print(f"计算AUC时出错: {str(e)}")
        auc = 0.5
    
    return {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'fpr': fpr,
        'fnr': fnr,
        'auc': auc
    }

def train(train_loader, device, model, criterion, optimizer, scheduler):
    model.train()
    train_loss = 0.0
    max_grad_norm = 0.05  # 降低梯度截断阈值到0.05
    
    for data in train_loader:
        node_features, adj_matrix, labels = [d.to(device) for d in data]
        
        # 检查输入数据
        if torch.isnan(node_features).any() or torch.isnan(adj_matrix).any():
            print("警告: 输入数据包含NaN，跳过当前批次")
            continue
            
        # 标准化输入数据
        node_features = (node_features - node_features.mean(dim=0)) / (node_features.std(dim=0) + 1e-8)
        node_features = torch.clamp(node_features, -2.0, 2.0)
        
        # 前向传播
        outputs = model(node_features, adj_matrix)
        
        # 检查输出
        if torch.isnan(outputs).any():
            print("警告: 模型输出包含NaN，跳过当前批次")
            continue
            
        loss = criterion(outputs, labels)
        
        # 检查损失值
        if torch.isnan(loss):
            print("警告: 损失值为NaN，跳过当前批次")
            continue
        
        # 反向传播
        optimizer.zero_grad()
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_grad_norm)
        
        # 检查梯度
        for name, param in model.named_parameters():
            if param.grad is not None:
                if torch.isnan(param.grad).any():
                    print(f"警告: 参数 {name} 的梯度包含NaN值，跳过当前批次")
                    optimizer.zero_grad()
                    continue
        
        optimizer.step()
        train_loss += loss.item()
    
    train_loss /= len(train_loader)
    return train_loss

def test(test_loader, device, model, criterion):
    """测试函数"""
    model.eval()
    total_loss = 0
    all_preds = []
    all_labels = []
    all_probs = []
    
    with torch.no_grad():
        for data in tqdm(test_loader, desc="测试"):
            node_features, adj_matrix, labels = [d.to(device) for d in data]
            
            # 前向传播
            outputs = model(node_features, adj_matrix)
            
            # 计算损失
            loss = criterion(outputs, labels)
            total_loss += loss.item()
            
            # 收集预测结果
            preds = outputs.argmax(dim=1)
            probs = F.softmax(outputs, dim=1)
            
            all_preds.extend(preds.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
            all_probs.extend(probs.cpu().numpy())
    
    # 计算测试指标
    test_metrics = calculate_metrics(
        np.array(all_labels),
        np.array(all_preds),
        np.array(all_probs)
    )
    
    # 添加损失到指标中
    test_metrics['loss'] = total_loss / len(test_loader)
    
    return test_metrics