import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import pickle
import numpy as np
from trainer import train, test
import os
import torch.nn.functional as F
from sklearn.model_selection import KFold
import json
import math
from sklearn.metrics import f1_score, precision_score, recall_score, accuracy_score, roc_auc_score, confusion_matrix
from datetime import datetime
from tqdm import tqdm
import warnings
import re
from collections import Counter
from concurrent.futures import ProcessPoolExecutor
from functools import partial
import random
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.manifold import TSNE
import tempfile
import mmap

# 忽略特定警告
warnings.filterwarnings("ignore", category=UserWarning)

# 设置随机种子
def set_seed(seed=42):
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)

# 在VulDataset类之前添加这个函数
def process_item(item, max_seq_len):
    """处理单个数据项的函数"""
    try:
        # 从AST图中提取特征
        nodes = item["nodes"]
        nodes_label = item.get("nodes_label", [])
        nodes_codes = item.get("nodes_codes", [])
        node_target = item.get("node_target", [])
        
        # 检查数据有效性
        if not nodes or not nodes_label or not nodes_codes:
            return None
        
        # 限制节点数量
        n_nodes = min(len(nodes), max_seq_len)
        nodes = nodes[:n_nodes]
        nodes_label = nodes_label[:n_nodes]
        nodes_codes = nodes_codes[:n_nodes]
        node_target = node_target[:n_nodes] if node_target else [0] * n_nodes
        
        # 构建节点特征向量
        node_features = []
        for i in range(n_nodes):
            label = nodes_label[i] if i < len(nodes_label) else ""
            code = nodes_codes[i] if i < len(nodes_codes) else ""
            target = node_target[i] if i < len(node_target) else 0
            
            # 提取代码特征
            features = extract_code_features(code)
            
            # 添加标签特征
            label_hash = hash(label) % 1000
            features[12] = label_hash / 1000.0  # 使用预留特征位置
            
            # 添加目标值
            features[13] = float(target)  # 使用预留特征位置
            
            node_features.append(features)
        
        node_features = np.array(node_features, dtype=np.float32)
        
        # 检查特征是否全为0
        if np.all(node_features == 0):
            return None
        
        # 特征标准化
        mean = np.mean(node_features, axis=0, keepdims=True)
        std = np.std(node_features, axis=0, keepdims=True) + 1e-8
        node_features = (node_features - mean) / std
        
        # 限制特征范围
        node_features = np.clip(node_features, -3.0, 3.0)
        
        label = item['target']
        
        # 处理图特征
        edges = item.get("edges", [])
        # 构建邻接矩阵
        adj_matrix = np.zeros((n_nodes, n_nodes), dtype=np.float32)
        if edges:
            for edge in edges:
                if edge[0] < n_nodes and edge[1] < n_nodes:
                    adj_matrix[edge[0]][edge[1]] = 1
        
        # 添加自环
        np.fill_diagonal(adj_matrix, 1.0)
        
        # 归一化邻接矩阵
        row_sum = adj_matrix.sum(axis=1, keepdims=True)
        row_sum[row_sum == 0] = 1
        adj_matrix = adj_matrix / row_sum
        
        return {
            'node_features': node_features,
            'label': label,
            'adj_matrix': adj_matrix
        }
        
    except Exception as e:
        print(f"警告: 处理数据项时出错: {str(e)}")
        return None

def extract_code_features(code):
    """提取代码特征"""
    features = []
    
    # 1. 基本统计特征 - 使用更高效的方法
    code_len = len(code)
    code_words = len(code.split())
    code_lines = len(code.splitlines())
    
    # 使用位运算优化特征计算
    has_numbers = any(c.isdigit() for c in code)
    has_special = any(not c.isalnum() for c in code)
    
    # 2. 使用Counter优化计数
    char_counter = Counter(code)
    bracket_count = char_counter['{'] + char_counter['['] + char_counter['(']
    
    # 3. 使用集合操作优化关键字匹配
    keywords = {'if', 'else', 'for', 'while', 'switch', 'case', 'break', 'continue', 'return'}
    code_words_set = set(code.split())
    keyword_count = len(keywords & code_words_set)
    
    # 4. 使用位运算优化操作符计数
    operators = {'+', '-', '*', '/', '=', '>', '<', '!'}
    operator_count = sum(char_counter[op] for op in operators)
    
    # 5. 使用更高效的正则表达式
    var_count = len(re.findall(r'\b[a-zA-Z_]\w*\b', code))
    func_count = len(re.findall(r'\b[a-zA-Z_]\w*\s*\(', code))
    
    # 6. 使用更高效的注释计数
    comment_count = sum(1 for line in code.splitlines() if line.strip().startswith(('//', '/*', '*', '*/')))
    
    # 7. 计算代码复杂度
    complexity = min((bracket_count + keyword_count + operator_count) / 50.0, 1.0)
    
    # 归一化特征
    features.extend([
        min(code_len, 1000) / 1000.0,        # 1. 代码长度
        min(code_words, 100) / 100.0,        # 2. 词数
        min(code_lines, 50) / 50.0,          # 3. 行数
        float(has_numbers),                  # 4. 是否包含数字
        float(has_special),                  # 5. 是否包含特殊字符
        min(bracket_count, 20) / 20.0,       # 6. 括号计数
        min(keyword_count, 10) / 10.0,       # 7. 关键字计数
        min(operator_count, 30) / 30.0,      # 8. 操作符计数
        min(var_count, 50) / 50.0,           # 9. 变量计数
        min(func_count, 20) / 20.0,          # 10. 函数计数
        min(comment_count, 10) / 10.0,       # 11. 注释计数
        complexity,                          # 12. 代码复杂度
        0.0,                                # 13. 预留特征
        0.0                                 # 14. 预留特征
    ])
    
    return features

def process_item_with_max_len(args):
    """处理单个数据项的函数，接收元组参数"""
    item, max_seq_len = args
    return process_item(item, max_seq_len)

class VulDataset(Dataset):
    def __init__(self, data, max_seq_len=512, augment=False, batch_size=100):
        self.data = data
        self.max_seq_len = max_seq_len
        self.augment = augment
        self.batch_size = batch_size
        
        # 使用列表存储处理后的数据
        self.processed_data = []
        self.metadata = []
        
        print("预计算数据集特征...")
        
        # 计算总样本数
        total_samples = len(data)
        processed_samples = 0
        
        # 创建总体进度条
        pbar = tqdm(total=total_samples, desc="处理数据集")
        
        # 分批处理数据
        total_batches = (len(data) + batch_size - 1) // batch_size
        for batch_idx in range(total_batches):
            start_idx = batch_idx * batch_size
            end_idx = min((batch_idx + 1) * batch_size, len(data))
            batch_data = data[start_idx:end_idx]
            
            batch_metadata = []
            
            for item in batch_data:
                try:
                    # 处理单个数据项
                    result = process_item(item, self.max_seq_len)
                    if result is not None:
                        # 优化数据存储
                        node_features = result['node_features']
                        adj_matrix = result['adj_matrix']
                        label = result['label']
                        
                        # 使用float16减少内存使用
                        node_features = node_features.astype(np.float16)
                        adj_matrix = adj_matrix.astype(np.float16)
                        
                        # 存储处理后的数据
                        self.processed_data.append({
                            'node_features': node_features,
                            'adj_matrix': adj_matrix,
                            'label': label
                        })
                        
                        # 记录元数据
                        batch_metadata.append({
                            'idx': len(self.processed_data) - 1,
                            'n_nodes': node_features.shape[0]
                        })
                        
                        # 清理内存
                        del result
                except Exception as e:
                    print(f"警告: 处理数据项时出错: {str(e)}")
                    continue
                
                # 更新进度条
                processed_samples += 1
                pbar.update(1)
                
                # 定期清理内存
                if processed_samples % 50 == 0:
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
            
            self.metadata.extend(batch_metadata)
            
            # 更新进度条描述
            pbar.set_postfix({
                '已处理样本': len(self.metadata),
                'GPU内存': f"{torch.cuda.memory_allocated()/1024**2:.2f}MB" if torch.cuda.is_available() else "N/A"
            })
        
        # 关闭进度条
        pbar.close()
        
        print(f"特征预计算完成，有效样本数: {len(self.metadata)}")
        
        # 打印数据集统计信息
        if self.metadata:
            node_counts = [m['n_nodes'] for m in self.metadata]
            print(f"节点数统计:")
            print(f"最小节点数: {min(node_counts)}")
            print(f"最大节点数: {max(node_counts)}")
            print(f"平均节点数: {sum(node_counts)/len(node_counts):.2f}")
            print(f"中位数节点数: {sorted(node_counts)[len(node_counts)//2]}")
    
    def __len__(self):
        return len(self.metadata)
    
    def __getitem__(self, idx):
        try:
            meta = self.metadata[idx]
            result = self.processed_data[meta['idx']]
            
            # 转换为张量，使用float32
            node_features = torch.tensor(result['node_features'], dtype=torch.float32)
            adj_matrix = torch.tensor(result['adj_matrix'], dtype=torch.float32)
            label = torch.tensor(result['label'], dtype=torch.long)
            
            # 数据增强
            if self.augment:
                node_features, adj_matrix, label = self.augment_data(node_features, adj_matrix, label)
            
            return node_features, adj_matrix, label
        except Exception as e:
            print(f"警告: 获取数据项时出错: {str(e)}")
            # 返回一个有效的默认值
            return torch.zeros((1, 14), dtype=torch.float32), \
                   torch.eye(1, dtype=torch.float32), \
                   torch.tensor(0, dtype=torch.long)
    
    def __del__(self):
        """清理内存"""
        try:
            del self.processed_data
            del self.metadata
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
        except Exception as e:
            print(f"警告: 清理内存时出错: {str(e)}")
    
    def augment_data(self, node_features, adj_matrix, label):
        """数据增强"""
        if not self.augment or label == 0:  # 只对正类样本进行增强
            return node_features, adj_matrix, label
        
        try:
            # 1. 随机丢弃边
            if np.random.random() < 0.3:  # 30%的概率丢弃边
                mask = torch.rand_like(adj_matrix) > 0.1
                mask = mask | mask.t()  # 保持对称性
                mask.fill_diagonal_(True)  # 保持自环
                adj_matrix = adj_matrix * mask
            
            # 2. 添加高斯噪声到节点特征
            if np.random.random() < 0.3:  # 30%的概率添加噪声
                noise = torch.randn_like(node_features) * 0.05
                node_features = node_features + noise
            
            # 3. 特征掩码
            if np.random.random() < 0.2:  # 20%的概率进行掩码
                mask = torch.rand_like(node_features) < 0.1
                node_features[mask] = 0
            
            return node_features, adj_matrix, label
        except Exception as e:
            print(f"警告: 数据增强时出错: {str(e)}")
            return node_features, adj_matrix, label

def collate_fn(batch):
    """自定义的collate函数，处理不同大小的图"""
    # 获取批次中最大的节点数，但不超过max_seq_len
    max_nodes = min(max(item[0].size(0) for item in batch), 512)  # 限制最大节点数
    feature_dim = batch[0][0].size(1)
    
    # 初始化填充后的张量
    batch_size = len(batch)
    padded_node_features = torch.zeros(batch_size, max_nodes, feature_dim)
    padded_adj_matrices = torch.zeros(batch_size, max_nodes, max_nodes)
    labels = torch.stack([item[2] for item in batch])
    
    # 填充每个样本
    for i, (node_features, adj_matrix, _) in enumerate(batch):
        n_nodes = min(node_features.size(0), max_nodes)  # 限制节点数
        # 填充节点特征
        padded_node_features[i, :n_nodes, :] = node_features[:n_nodes]
        # 填充邻接矩阵
        padded_adj_matrices[i, :n_nodes, :n_nodes] = adj_matrix[:n_nodes, :n_nodes]
        # 添加自环
        padded_adj_matrices[i, :n_nodes, :n_nodes] += torch.eye(n_nodes)
    
    return padded_node_features, padded_adj_matrices, labels

def create_data_loaders(train_data, valid_data, test_data, batch_size=32, num_workers=4):
    """创建数据加载器"""
    # 创建数据集
    train_dataset = VulDataset(train_data, max_seq_len=512, augment=True)  # 增加最大序列长度
    valid_dataset = VulDataset(valid_data, max_seq_len=512, augment=False)
    test_dataset = VulDataset(test_data, max_seq_len=512, augment=False)
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        collate_fn=collate_fn
    )
    
    valid_loader = DataLoader(
        valid_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        collate_fn=collate_fn
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        collate_fn=collate_fn
    )
    
    return train_loader, valid_loader, test_loader

class MultiHeadAttention(nn.Module):
    def __init__(self, hidden_dim, num_heads=4):
        super(MultiHeadAttention, self).__init__()
        self.num_heads = num_heads
        self.head_dim = hidden_dim // num_heads
        
        self.query = nn.Linear(hidden_dim, hidden_dim)
        self.key = nn.Linear(hidden_dim, hidden_dim)
        self.value = nn.Linear(hidden_dim, hidden_dim)
        self.proj = nn.Linear(hidden_dim, hidden_dim)
        
    def forward(self, x, mask=None):
        batch_size = x.size(0)
        
        # 线性变换
        q = self.query(x).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        k = self.key(x).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        v = self.value(x).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        
        # 计算注意力分数
        scores = torch.matmul(q, k.transpose(-2, -1)) / math.sqrt(self.head_dim)
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
        attn = F.softmax(scores, dim=-1)
        
        # 应用注意力
        out = torch.matmul(attn, v)
        out = out.transpose(1, 2).contiguous().view(batch_size, -1, self.num_heads * self.head_dim)
        return self.proj(out)

class GNNLayer(nn.Module):
    def __init__(self, in_dim, out_dim):
        super(GNNLayer, self).__init__()
        self.linear = nn.Linear(in_dim, out_dim)
        self.norm1 = nn.LayerNorm(out_dim)
        self.norm3 = nn.LayerNorm(out_dim)
        self.ffn = nn.Sequential(
            nn.Linear(out_dim, out_dim * 4),
            nn.ReLU(),
            nn.Linear(out_dim * 4, out_dim)
        )
        
    def forward(self, x, adj):
        h = torch.matmul(adj, x)
        h = self.linear(h)
        h = self.norm1(h)
        ffn_out = self.ffn(h)
        h = self.norm3(h + ffn_out)
        return h

class PolicyNetwork(nn.Module):
    def __init__(self, input_dim, hidden_dim=256):
        super(PolicyNetwork, self).__init__()
        # 简化网络结构
        self.actor = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1),
            nn.Sigmoid()
        )
        
        self.critic = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1),
            nn.Tanh()
        )
        
        # 使用更保守的初始化
        self.apply(self._init_weights)
        
    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            nn.init.xavier_uniform_(module.weight, gain=0.01)
            if module.bias is not None:
                nn.init.constant_(module.bias, 0)
        
    def forward(self, x):
        # 简单的输入标准化
        x = torch.clamp(x, -0.5, 0.5)
        action_probs = self.actor(x)
        state_value = self.critic(x)
        return action_probs, state_value

class PPO:
    def __init__(self, policy_network, lr=0.0003, gamma=0.99, epsilon=0.2, value_coef=0.5, entropy_coef=0.01):
        self.policy_network = policy_network
        self.optimizer = optim.AdamW(policy_network.parameters(), lr=lr, weight_decay=1e-5)
        self.gamma = gamma
        self.epsilon = epsilon
        self.value_coef = value_coef
        self.entropy_coef = entropy_coef
        self.device = next(policy_network.parameters()).device
        self.max_grad_norm = 0.05
        self.early_stop_patience = 5
        self.early_stop_counter = 0
        self.best_reward = float('-inf')
        self.update_frequency = 4  # 每4个batch更新一次PPO
        self.batch_counter = 0
        
    def compute_gae(self, rewards, values, next_value, dones):
        """简化的广义优势估计计算"""
        advantages = []
        gae = 0
        
        # 简单的奖励标准化
        rewards = torch.clamp(rewards, -0.5, 0.5)
        
        for r, v, done in zip(reversed(rewards), reversed(values), reversed(dones)):
            if done:
                gae = 0
            delta = r + self.gamma * next_value * (1 - done) - v
            delta = torch.clamp(delta, -0.5, 0.5)
            gae = delta + self.gamma * (1 - done) * gae
            advantages.insert(0, gae)
            next_value = v
            
        advantages = torch.tensor(advantages, device=self.device)
        advantages = torch.clamp(advantages, -0.5, 0.5)
        return advantages
    
    def update(self, states, actions, old_probs, rewards, dones):
        self.batch_counter += 1
        if self.batch_counter % self.update_frequency != 0:
            return 0.0
            
        # 计算平均奖励
        mean_reward = rewards.mean().item()
        
        # 早停检查
        if mean_reward > self.best_reward:
            self.best_reward = mean_reward
            self.early_stop_counter = 0
        else:
            self.early_stop_counter += 1
            
        if self.early_stop_counter >= self.early_stop_patience:
            return 0.0
        
        # 基本的数据检查
        if torch.isnan(states).any() or torch.isnan(actions).any() or \
           torch.isnan(old_probs).any() or torch.isnan(rewards).any():
            return 0.0
        
        # 简单的状态标准化
        states = torch.clamp(states, -0.5, 0.5)
        
        # 计算优势函数
        with torch.no_grad():
            _, values = self.policy_network(states)
            next_value = values[-1]
            advantages = self.compute_gae(rewards, values, next_value, dones)
            returns = advantages + values
        
        # 计算新的动作概率和状态值
        new_probs, new_values = self.policy_network(states)
        
        # 简化的比率计算
        ratio = torch.clamp(new_probs / (old_probs + 1e-8), 0.0, 1.0)
        
        # 简化的PPO目标
        surr1 = ratio * advantages
        surr2 = torch.clamp(ratio, 1 - self.epsilon, 1 + self.epsilon) * advantages
        policy_loss = -torch.min(surr1, surr2).mean()
        
        # 简化的价值损失
        value_loss = F.mse_loss(new_values, returns)
        
        # 简化的熵损失
        entropy = -(new_probs * torch.log(new_probs + 1e-10) + 
                   (1 - new_probs) * torch.log(1 - new_probs + 1e-10)).mean()
        
        # 总损失
        loss = policy_loss + self.value_coef * value_loss - self.entropy_coef * entropy
        
        # 更新网络
        self.optimizer.zero_grad()
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(self.policy_network.parameters(), self.max_grad_norm)
        
        # 检查梯度
        for param in self.policy_network.parameters():
            if param.grad is not None and torch.isnan(param.grad).any():
                return 0.0
                
        self.optimizer.step()
        return loss.item()

class VulModel(nn.Module):
    def __init__(self, input_dim=14, graph_dim=32, hidden_dim=128, num_classes=2):
        super(VulModel, self).__init__()
        
        # 输入层
        self.input_layer = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        # 图神经网络层
        self.gnn_layers = nn.ModuleList([
            GNNLayer(hidden_dim, hidden_dim) for _ in range(3)
        ])
        
        # 层归一化
        self.layer_norms = nn.ModuleList([
            nn.LayerNorm(hidden_dim) for _ in range(3)
        ])
        
        # 多头注意力层
        self.attention = MultiHeadAttention(hidden_dim, num_heads=4)
        
        # 残差连接后的层归一化
        self.final_norm = nn.LayerNorm(hidden_dim)
        
        # 全局池化层
        self.global_pool = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        # 输出层
        self.output_layer = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.LayerNorm(hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim // 2, num_classes)
        )
        
        # 初始化权重
        self._init_weights()
        
    def _init_weights(self):
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
    
    def forward(self, node_features, adj_matrix):
        batch_size = node_features.size(0)
        
        # 输入层处理
        x = self.input_layer(node_features)
        
        # 图神经网络层处理（带残差连接）
        for i, (gnn_layer, layer_norm) in enumerate(zip(self.gnn_layers, self.layer_norms)):
            residual = x
            x = gnn_layer(x, adj_matrix)
            x = layer_norm(x)
            x = F.relu(x)
            x = x + residual  # 残差连接
        
        # 多头注意力处理
        x = self.attention(x)
        x = self.final_norm(x)
        
        # 全局池化
        x = torch.mean(x, dim=1)  # 平均池化
        x = self.global_pool(x)
        
        # 输出层处理
        x = self.output_layer(x)
        
        return x

class FocalLoss(nn.Module):
    def __init__(self, alpha=0.75, gamma=2.0, reduction='mean'):
        """
        Focal Loss实现
        Args:
            alpha: 类别权重，用于处理类别不平衡
            gamma: 聚焦参数，用于降低易分类样本的权重
            reduction: 损失计算方式，可选 'mean', 'sum', 'none'
        """
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
        
    def forward(self, inputs, targets):
        """
        计算Focal Loss
        Args:
            inputs: 模型输出 [batch_size, num_classes]
            targets: 真实标签 [batch_size]
        """
        # 计算交叉熵损失
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        
        # 获取预测概率
        pt = torch.exp(-ce_loss)
        
        # 计算focal loss
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
        
        # 根据reduction方式返回损失
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss

def k_fold_cross_validation(train_valid_data, device, n_splits=5, random_state=42):
    """执行k-fold交叉验证，只使用训练集和验证集"""
    print(f"\n开始{n_splits}折交叉验证...")
    
    # 检查数据是否为空
    if not train_valid_data:
        raise ValueError("训练数据为空")
    
    # 按标签分组
    label_groups = {}
    for item in train_valid_data:
        label = item.get('target')
        if label is None:
            print(f"警告: 样本缺少target字段")
            continue
        if label not in label_groups:
            label_groups[label] = []
        label_groups[label].append(item)
    
    # 检查标签分布
    print("\n标签分布:")
    for label, items in label_groups.items():
        print(f"标签 {label}: {len(items)} 个样本")
    
    # 对每个标签组进行分层抽样
    folds = [[] for _ in range(n_splits)]
    for label, items in label_groups.items():
        np.random.seed(random_state)
        np.random.shuffle(items)
        fold_size = len(items) // n_splits
        for i in range(n_splits):
            start_idx = i * fold_size
            end_idx = start_idx + fold_size if i < n_splits - 1 else len(items)
            folds[i].extend(items[start_idx:end_idx])
    
    # 检查每个折的分布
    print("\n各折数据分布:")
    for i, fold in enumerate(folds):
        labels = [item['target'] for item in fold]
        label_counts = np.bincount(labels)
        print(f"第{i+1}折:")
        for label, count in enumerate(label_counts):
            print(f"  标签 {label}: {count} 个样本")
    
    # 执行交叉验证
    fold_metrics = []
    for fold_idx in range(n_splits):
        print(f"\n训练第{fold_idx + 1}折...")
        
        # 准备训练集和验证集
        valid_data = folds[fold_idx]
        train_data = []
        for i in range(n_splits):
            if i != fold_idx:
                train_data.extend(folds[i])
        
        # 检查训练集和验证集的分布
        train_labels = [item['target'] for item in train_data]
        valid_labels = [item['target'] for item in valid_data]
        print(f"\n第{fold_idx + 1}折数据分布:")
        print(f"训练集标签分布: {np.bincount(train_labels)}")
        print(f"验证集标签分布: {np.bincount(valid_labels)}")
        
        # 创建数据集
        train_dataset = VulDataset(train_data, max_seq_len=512, augment=True, batch_size=200)
        valid_dataset = VulDataset(valid_data, max_seq_len=512, augment=False, batch_size=200)
        
        # 创建数据加载器
        train_loader = DataLoader(
            train_dataset,
            batch_size=128,
            shuffle=True,
            num_workers=8,
            collate_fn=collate_fn,
            pin_memory=True
        )
        
        valid_loader = DataLoader(
            valid_dataset,
            batch_size=128,
            shuffle=False,
            num_workers=8,
            collate_fn=collate_fn,
            pin_memory=True
        )
        
        # 创建模型
        model = VulModel(
            input_dim=14,
            graph_dim=32,
            hidden_dim=64,
            num_classes=2
        ).to(device)
        
        # 计算类别权重
        train_labels = []
        for batch in train_loader:
            _, _, labels = batch
            train_labels.extend(labels.cpu().numpy())
        
        if not train_labels:
            print("警告: 训练集为空")
            continue
            
        label_counts = np.bincount(train_labels)
        if len(label_counts) < 2:
            print(f"警告: 标签分布不均衡，只有{len(label_counts)}个类别")
            continue
            
        # 计算类别权重，使用更保守的权重计算方式
        pos_weight = min(label_counts[0] / label_counts[1], 5.0)  # 限制最大权重
        class_weights = torch.tensor([1.0, pos_weight], dtype=torch.float32, device=device)
        print(f"类别权重: {class_weights.tolist()}")
        
        # 训练模型
        criterion = nn.CrossEntropyLoss(weight=class_weights)
        optimizer = optim.AdamW(
            model.parameters(),
            lr=0.0001,
            weight_decay=0.1,
            betas=(0.9, 0.999)
        )
        
        # 使用余弦退火学习率调度器
        scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
            optimizer,
            T_0=5,
            T_mult=2,
            eta_min=1e-6
        )
        
        # 修改训练参数
        best_f1 = train_fold(
            train_loader, 
            valid_loader, 
            device, 
            model, 
            criterion, 
            optimizer, 
            scheduler, 
            fold_idx,
            validate_every=2,
            num_epochs=30,
            patience=5
        )
        
        # 记录该折的详细结果
        print(f"\n第{fold_idx + 1}折训练结果:")
        print(f"最佳F1分数: {best_f1*100:.2f}%")
        
        fold_metrics.append(best_f1)
        
        # 清理GPU内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    # 检查是否有有效的指标
    if not fold_metrics:
        raise ValueError("没有有效的交叉验证结果")
    
    # 计算平均指标
    mean_f1 = np.mean(fold_metrics)
    std_f1 = np.std(fold_metrics)
    
    print(f"\n交叉验证结果:")
    print(f"平均F1分数: {mean_f1*100:.2f}% ± {std_f1*100:.2f}%")
    print(f"各折F1分数: {[f'{m*100:.2f}%' for m in fold_metrics]}")
    
    return mean_f1, std_f1

def generate_adversarial_sample(code):
    """生成对抗样本"""
    if not code or not isinstance(code, str):
        return code
        
    try:
        # 1. 随机选择一种对抗方法
        method = np.random.choice([
            'add_noise',
            'remove_comments',
            'rename_variables',
            'add_dead_code'
        ])
        
        if method == 'add_noise':
            # 添加随机空格和换行
            lines = code.split('\n')
            for i in range(len(lines)):
                if np.random.random() < 0.3:  # 30%的概率添加噪声
                    lines[i] = ' ' * np.random.randint(1, 5) + lines[i] + ' ' * np.random.randint(1, 5)
            return '\n'.join(lines)
            
        elif method == 'remove_comments':
            # 移除注释
            lines = code.split('\n')
            return '\n'.join(line for line in lines if not line.strip().startswith(('//', '/*', '*', '*/')))
            
        elif method == 'rename_variables':
            # 重命名变量
            words = code.split()
            for i in range(len(words)):
                if words[i].isidentifier() and np.random.random() < 0.3:  # 30%的概率重命名
                    words[i] = f"var_{np.random.randint(1000)}"
            return ' '.join(words)
            
        else:  # add_dead_code
            # 添加死代码
            dead_code = [
                "if (false) { return; }",
                "while (0) { break; }",
                "for (int i = 0; i < 0; i++) { continue; }"
            ]
            if np.random.random() < 0.5:
                return code + '\n' + np.random.choice(dead_code)
            return np.random.choice(dead_code) + '\n' + code
            
    except Exception as e:
        print(f"生成对抗样本时出错: {str(e)}")
        return code

def test_adversarial_samples(model, test_data, device):
    """测试对抗样本"""
    print("开始对抗样本测试...")
    
    # 创建对抗样本
    adversarial_data = []
    for item in test_data:
        adv_item = item.copy()
        if 'nodes_codes' in adv_item:
            adv_item['nodes_codes'] = [generate_adversarial_sample(code) for code in adv_item['nodes_codes']]
        adversarial_data.append(adv_item)
    
    # 创建数据集
    adv_dataset = VulDataset(adversarial_data, max_seq_len=32, augment=False)
    adv_loader = DataLoader(
        adv_dataset,
        batch_size=32,
        shuffle=False,
        num_workers=4,
        collate_fn=collate_fn
    )
    
    # 测试模型
    criterion = FocalLoss(alpha=0.75, gamma=2.0)
    adv_metrics = test(adv_loader, device, model, criterion)
    
    print("\n对抗样本测试结果:")
    print(f"F1分数: {adv_metrics['f1']*100:.2f}%")
    print(f"准确率: {adv_metrics['accuracy']*100:.2f}%")
    print(f"精确率: {adv_metrics['precision']*100:.2f}%")
    print(f"召回率: {adv_metrics['recall']*100:.2f}%")
    
    return adv_metrics

def visualize_attention_weights(model, test_loader, device, save_dir='./visualizations'):
    """分析注意力权重"""
    model.eval()
    
    # 限制处理的样本数量
    max_samples = 1000
    processed_samples = 0
    
    # 分批收集数据
    batch_size = 32
    all_attention_weights = []
    all_predictions = []
    all_labels = []
    
    print("开始收集注意力权重...")
    try:
        with torch.no_grad():
            for batch in tqdm(test_loader, desc="收集注意力权重"):
                if processed_samples >= max_samples:
                    break
                    
                node_features, adj_matrix, labels = [x.to(device) for x in batch]
                
                # 获取模型输出
                outputs = model(node_features, adj_matrix)
                predictions = outputs.argmax(dim=1)
                
                # 获取注意力权重
                batch_size = node_features.size(0)
                node_features = node_features.view(-1, node_features.size(-1))
                node_features = model.node_encoder(node_features)
                node_features = node_features.view(batch_size, -1, node_features.size(-1))
                
                for gnn_layer in model.gnn_layers:
                    node_features = gnn_layer(node_features, adj_matrix)
                
                attention_weights = model.attention(node_features)
                attention_weights = F.softmax(attention_weights, dim=1)
                
                # 收集数据
                all_attention_weights.extend(attention_weights.cpu().numpy())
                all_predictions.extend(predictions.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
                
                processed_samples += batch_size
                
                # 定期清理内存
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
        
        # 转换为numpy数组
        all_attention_weights = np.array(all_attention_weights)
        all_predictions = np.array(all_predictions)
        all_labels = np.array(all_labels)
        
        # 计算统计信息
        attention_weights_flat = all_attention_weights.reshape(-1)
        correct_mask = np.repeat(all_predictions == all_labels, all_attention_weights.shape[1])
        
        # 计算高注意力节点的预测准确率
        high_attention_mask = attention_weights_flat > np.percentile(attention_weights_flat, 90)
        high_attention_correct = (all_predictions[high_attention_mask[:len(all_predictions)]] == 
                                all_labels[high_attention_mask[:len(all_labels)]]).mean()
        
        # 打印统计信息
        print("\n注意力权重分析:")
        print(f"处理样本数: {processed_samples}")
        print(f"平均注意力权重: {all_attention_weights.mean():.4f}")
        print(f"注意力权重标准差: {all_attention_weights.std():.4f}")
        print(f"最大注意力权重: {all_attention_weights.max():.4f}")
        print(f"最小注意力权重: {all_attention_weights.min():.4f}")
        print(f"高注意力节点的预测准确率: {high_attention_correct:.4f}")
        
        # 准备返回结果
        result = {
            'processed_samples': processed_samples,
            'mean_attention': float(all_attention_weights.mean()),
            'std_attention': float(all_attention_weights.std()),
            'max_attention': float(all_attention_weights.max()),
            'min_attention': float(all_attention_weights.min()),
            'high_attention_correct': float(high_attention_correct)
        }
        
    except Exception as e:
        print(f"处理注意力权重时出错: {str(e)}")
        result = {
            'processed_samples': processed_samples,
            'mean_attention': 0.0,
            'std_attention': 0.0,
            'max_attention': 0.0,
            'min_attention': 0.0,
            'high_attention_correct': 0.0
        }
    
    finally:
        # 清理内存
        if 'all_attention_weights' in locals():
            del all_attention_weights
        if 'all_predictions' in locals():
            del all_predictions
        if 'all_labels' in locals():
            del all_labels
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    return result

def load_processed_data(data_file):
    """加载处理后的数据文件"""
    try:
        data = []
        with open(data_file, 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip():  # 跳过空行
                    data.append(json.loads(line))
        return data
    except Exception as e:
        print(f"加载处理后的数据文件时出错: {str(e)}")
        return []

def calculate_metrics(y_true, y_pred, y_prob):
    """计算各种评估指标"""
    # 计算混淆矩阵
    tn, fp, fn, tp = confusion_matrix(y_true, y_pred).ravel()
    
    # 计算基本指标
    accuracy = (tp + tn) / (tp + tn + fp + fn)
    precision = tp / (tp + fp) if (tp + fp) > 0 else 0
    recall = tp / (tp + fn) if (tp + fn) > 0 else 0
    f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
    
    # 计算FPR和FNR
    fpr = fp / (fp + tn) if (fp + tn) > 0 else 0
    fnr = fn / (fn + tp) if (fn + tp) > 0 else 0
    
    # 计算AUC
    try:
        if y_prob is not None and len(y_prob.shape) > 1 and y_prob.shape[1] > 1:
            # 确保使用正类的概率
            y_prob_positive = y_prob[:, 1]
            
            # 检查概率分布
            if np.all(y_prob_positive == 0.5):
                print("警告: 所有预测概率都是0.5")
                auc = 0.5
            else:
                # 确保y_true和y_prob长度一致
                if len(y_true) == len(y_prob_positive):
                    auc = roc_auc_score(y_true, y_prob_positive)
                    print(f"AUC计算信息:")
                    print(f"- 正类概率均值: {y_prob_positive.mean():.4f}")
                    print(f"- 正类概率标准差: {y_prob_positive.std():.4f}")
                else:
                    print(f"警告: y_true长度({len(y_true)})与y_prob长度({len(y_prob_positive)})不一致")
                    auc = 0.5
        else:
            print(f"警告: 预测概率格式无效 - 形状: {y_prob.shape if y_prob is not None else None}")
            auc = 0.5
    except Exception as e:
        print(f"计算AUC时出错: {str(e)}")
        auc = 0.5
    
    return {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'fpr': fpr,
        'fnr': fnr,
        'auc': auc
    }

def validate(valid_loader, model, criterion, device):
    """验证函数"""
    model.eval()
    total_loss = 0
    all_preds = []
    all_labels = []
    all_probs = []
    
    with torch.no_grad():
        for batch_idx, (node_features, adj_matrix, labels) in enumerate(valid_loader):
            node_features = node_features.to(device)
            labels = labels.to(device)
            adj_matrix = adj_matrix.to(device)
            
            # 前向传播
            outputs = model(node_features, adj_matrix)
            
            # 计算损失
            loss = criterion(outputs, labels)
            total_loss += loss.item()
            
            # 收集预测结果和概率
            preds = outputs.argmax(dim=1)
            probs = F.softmax(outputs, dim=1)  # 使用softmax获取概率
            
            # 确保概率是二维的 [batch_size, num_classes]
            if len(probs.shape) == 1:
                probs = probs.unsqueeze(1)
            
            all_preds.extend(preds.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
            all_probs.extend(probs.cpu().numpy())
    
    # 转换为numpy数组
    all_labels = np.array(all_labels)
    all_preds = np.array(all_preds)
    all_probs = np.array(all_probs)
    
    # 打印调试信息
    print(f"\n验证集统计:")
    print(f"标签分布: {np.bincount(all_labels)}")
    print(f"预测分布: {np.bincount(all_preds)}")
    print(f"概率分布形状: {all_probs.shape}")
    if len(all_probs.shape) > 1 and all_probs.shape[1] > 1:
        print(f"正类概率范围: [{all_probs[:, 1].min():.4f}, {all_probs[:, 1].max():.4f}]")
        print(f"正类概率均值: {all_probs[:, 1].mean():.4f}")
        print(f"正类概率标准差: {all_probs[:, 1].std():.4f}")
    
    # 计算验证指标
    val_metrics = calculate_metrics(all_labels, all_preds, all_probs)
    
    # 添加损失到指标中
    val_metrics['loss'] = total_loss / len(valid_loader)
    
    return val_metrics

def test_with_noise(model, test_loader, device, noise_level):
    """使用不同级别的噪声测试模型"""
    model.eval()
    all_preds = []
    all_labels = []
    all_probs = []
    
    with torch.no_grad():
        for batch in test_loader:
            node_features, adj_matrix, labels = [x.to(device) for x in batch]
            
            # 添加高斯噪声
            noise = torch.randn_like(node_features) * noise_level
            noisy_features = node_features + noise
            noisy_features = torch.clamp(noisy_features, -1, 1)
            
            # 前向传播
            outputs = model(noisy_features, adj_matrix)
            preds = outputs.argmax(dim=1)
            probs = F.softmax(outputs, dim=1)  # 使用softmax获取概率
            
            all_preds.extend(preds.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
            all_probs.extend(probs.cpu().numpy())
    
    return calculate_metrics(
        np.array(all_labels),
        np.array(all_preds),
        np.array(all_probs)  # 传入预测概率
    )

def test_with_perturbation(model, test_loader, device, pert_level):
    """使用不同级别的特征扰动测试模型"""
    model.eval()
    all_preds = []
    all_labels = []
    all_probs = []
    
    with torch.no_grad():
        for batch in test_loader:
            node_features, adj_matrix, labels = [x.to(device) for x in batch]
            
            # 生成随机扰动
            perturbation = torch.randn_like(node_features) * pert_level
            perturbed_features = node_features + perturbation
            perturbed_features = torch.clamp(perturbed_features, -1, 1)
            
            # 前向传播
            outputs = model(perturbed_features, adj_matrix)
            preds = outputs.argmax(dim=1)
            probs = F.softmax(outputs, dim=1)  # 使用softmax获取概率
            
            all_preds.extend(preds.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
            all_probs.extend(probs.cpu().numpy())
    
    return calculate_metrics(
        np.array(all_labels),
        np.array(all_preds),
        np.array(all_probs)  # 传入预测概率
    )

def train_fold(train_loader, valid_loader, device, model, criterion, optimizer, scheduler, fold_idx, validate_every=2, num_epochs=30, patience=5):
    """训练单个fold，结合对抗训练和PPO强化学习"""
    # 设置早停参数
    min_delta = 0.0001
    best_val_loss = float('inf')
    best_f1 = 0.0
    patience_counter = 0
    best_model_state = None
    
    # 设置L1正则化系数
    l1_lambda = 0.0005  # 减小正则化强度
    
    # 创建PPO策略网络和优化器
    policy_network = PolicyNetwork(input_dim=14, hidden_dim=64).to(device)
    ppo = PPO(
        policy_network=policy_network,
        lr=0.0002,  # 增加学习率
        gamma=0.99,
        epsilon=0.1,
        value_coef=0.5,
        entropy_coef=0.01
    )
    
    # 设置梯度累积步数
    accumulation_steps = 2  # 减少梯度累积步数
    
    # 初始化混合精度训练
    scaler = torch.cuda.amp.GradScaler()
    
    # 创建日志文件
    log_dir = os.path.join('logs', f'fold_{fold_idx}')
    os.makedirs(log_dir, exist_ok=True)
    log_file = os.path.join(log_dir, 'training.log')
    
    # 记录训练参数
    with open(log_file, 'w') as f:
        f.write(f"训练参数:\n")
        f.write(f"批次大小: {train_loader.batch_size}\n")
        f.write(f"学习率: {optimizer.param_groups[0]['lr']}\n")
        f.write(f"权重衰减: {optimizer.param_groups[0]['weight_decay']}\n")
        f.write(f"早停耐心值: {patience}\n")
        f.write(f"L1正则化系数: {l1_lambda}\n")
        f.write(f"验证频率: {validate_every}\n")
        f.write(f"梯度累积步数: {accumulation_steps}\n")
        f.write(f"使用混合精度训练: True\n")
        f.write("\n训练日志:\n")
    
    # 训练循环
    for epoch in range(num_epochs):
        model.train()
        total_loss = 0
        all_preds = []
        all_labels = []
        all_probs = []
        
        # 动态调整对抗训练参数
        epsilon = min(0.02 + epoch * 0.003, 0.08)  # 增加初始epsilon和增长率
        adv_weight = min(0.1 + epoch * 0.01, 0.3)  # 增加初始权重和增长率
        
        # 训练一个epoch
        optimizer.zero_grad()  # 在每个epoch开始时清零梯度
        for batch_idx, batch in enumerate(train_loader):
            node_features, adj_matrix, labels = [x.to(device) for x in batch]
            
            # 计算类别权重
            pos_weight = torch.tensor([1.0, 3.0], device=device)  # 增加正类权重
            
            # 使用混合精度训练
            with torch.cuda.amp.autocast():
                # 1. 原始样本训练
                outputs = model(node_features, adj_matrix)
                loss = criterion(outputs, labels)
                
                # 2. 生成对抗样本
                node_features.requires_grad_(True)
                outputs_adv = model(node_features, adj_matrix)
                loss_adv = criterion(outputs_adv, labels)
                
                # 计算梯度
                model.zero_grad()
                loss_adv.backward()
                
                # 生成对抗扰动
                perturbation = epsilon * node_features.grad.sign()
                
                # 添加随机噪声增强鲁棒性
                noise = torch.randn_like(perturbation) * 0.01  # 减小噪声
                perturbation = perturbation + noise
                
                # 生成对抗样本
                node_features_adv = node_features.detach() + perturbation
                node_features_adv = torch.clamp(node_features_adv, -1, 1)
                
                # 清理梯度
                model.zero_grad()
                node_features.requires_grad_(False)
                
                # 3. 对抗样本训练
                outputs_adv = model(node_features_adv, adj_matrix)
                loss_adv = criterion(outputs_adv, labels)
                
                # 4. 组合损失
                loss = (1 - adv_weight) * loss + adv_weight * loss_adv
                
                # 添加L1正则化
                l1_norm = sum(p.abs().sum() for p in model.parameters())
                loss = loss + l1_lambda * l1_norm
                
                # 根据梯度累积步数缩放损失
                loss = loss / accumulation_steps
            
            # 使用scaler进行反向传播
            scaler.scale(loss).backward()
            
            # 梯度累积
            if (batch_idx + 1) % accumulation_steps == 0:
                # 梯度裁剪
                scaler.unscale_(optimizer)
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)  # 增加梯度裁剪阈值
                
                # 检查梯度
                grad_norm = torch.norm(torch.stack([p.grad.norm() for p in model.parameters() if p.grad is not None]))
                if grad_norm > 10.0:  # 增加梯度范数阈值
                    print(f"警告: 梯度范数过大 ({grad_norm:.2f})，跳过更新")
                    optimizer.zero_grad()
                    continue
                
                # 更新参数
                scaler.step(optimizer)
                scaler.update()
                optimizer.zero_grad()
            
            total_loss += loss.item() * accumulation_steps
            
            # 收集预测结果和概率
            with torch.no_grad():
                preds = outputs.argmax(dim=1)
                probs = F.softmax(outputs, dim=1)
                
                # 确保概率是二维的 [batch_size, num_classes]
                if len(probs.shape) == 1:
                    probs = probs.unsqueeze(1)
                
                all_preds.extend(preds.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
                all_probs.extend(probs.cpu().numpy())
            
            # 打印训练进度
            if (batch_idx + 1) % 10 == 0:
                print(f"Epoch {epoch+1}/{num_epochs}, Batch {batch_idx+1}/{len(train_loader)}, "
                      f"Loss: {loss.item()*accumulation_steps:.4f}")
        
        # 转换为numpy数组
        all_labels = np.array(all_labels)
        all_preds = np.array(all_preds)
        all_probs = np.array(all_probs)
        
        # 计算训练指标
        train_metrics = calculate_metrics(all_labels, all_preds, all_probs)
        
        # 验证
        if (epoch + 1) % validate_every == 0:
            model.eval()
            val_loss = 0
            val_preds = []
            val_labels = []
            val_probs = []
            
            with torch.no_grad():
                for batch in valid_loader:
                    node_features, adj_matrix, labels = [x.to(device) for x in batch]
                    
                    # 使用混合精度进行验证
                    with torch.cuda.amp.autocast():
                        outputs = model(node_features, adj_matrix)
                        loss = criterion(outputs, labels)
                    
                    val_loss += loss.item()
                    
                    preds = outputs.argmax(dim=1)
                    probs = F.softmax(outputs, dim=1)
                    
                    # 确保概率是二维的 [batch_size, num_classes]
                    if len(probs.shape) == 1:
                        probs = probs.unsqueeze(1)
                    
                    val_preds.extend(preds.cpu().numpy())
                    val_labels.extend(labels.cpu().numpy())
                    val_probs.extend(probs.cpu().numpy())
            
            # 转换为numpy数组
            val_labels = np.array(val_labels)
            val_preds = np.array(val_preds)
            val_probs = np.array(val_probs)
            
            # 计算验证指标
            val_metrics = calculate_metrics(val_labels, val_preds, val_probs)
            val_metrics['loss'] = val_loss / len(valid_loader)
            
            # 检查模型是否完全偏向负类
            if val_metrics['recall'] == 0 and val_metrics['precision'] == 0:
                print("\n警告: 模型完全偏向负类，重新初始化模型...")
                # 重新初始化模型
                model.apply(model._init_weights)
                # 重置优化器
                optimizer = optim.AdamW(
                    model.parameters(),
                    lr=0.0001,  # 增加学习率
                    weight_decay=0.05,  # 减小权重衰减
                    betas=(0.9, 0.999)
                )
                # 重置学习率调度器
                scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
                    optimizer,
                    T_0=3,  # 减少周期
                    T_mult=2,
                    eta_min=1e-6
                )
                # 增加正类权重
                pos_weight = torch.tensor([1.0, 4.0], device=device)  # 进一步增加正类权重
                criterion = nn.CrossEntropyLoss(weight=pos_weight)
                continue
            
            # 记录日志
            with open(log_file, 'a') as f:
                f.write(f"\nEpoch {epoch+1}:\n")
                f.write(f"训练损失: {total_loss/len(train_loader):.4f}\n")
                f.write(f"训练指标: {train_metrics}\n")
                f.write(f"验证损失: {val_metrics['loss']:.4f}\n")
                f.write(f"验证指标: {val_metrics}\n")
                f.write(f"当前epsilon: {epsilon:.4f}\n")
                f.write(f"当前对抗样本权重: {adv_weight:.4f}\n")
            
            # 早停检查
            if val_metrics['f1'] > best_f1 + min_delta:
                best_f1 = val_metrics['f1']
                best_val_loss = val_metrics['loss']
                patience_counter = 0
                best_model_state = model.state_dict().copy()
            else:
                patience_counter += 1
                if patience_counter >= patience:
                    print(f"\n早停触发! {patience} 个epoch没有改善")
                    break
            
            # 打印验证结果
            print(f"\n验证结果 (Epoch {epoch+1}):")
            print(f"验证损失: {val_metrics['loss']:.4f}")
            print(f"验证指标: {val_metrics}")
            print(f"当前epsilon: {epsilon:.4f}")
            print(f"当前对抗样本权重: {adv_weight:.4f}")
        
        # 更新学习率
        scheduler.step()
    
    # 保存最佳模型
    if best_model_state is not None:
        model.load_state_dict(best_model_state)
        torch.save(model.state_dict(), os.path.join(log_dir, 'best_model.pth'))
    
    return best_f1

def generate_fgsm_attack(model, node_features, adj_matrix, labels, epsilon=0.1):
    """使用FGSM生成对抗样本"""
    model.eval()
    
    # 确保所有输入都需要梯度
    node_features = node_features.clone().detach().requires_grad_(True)
    adj_matrix = adj_matrix.clone().detach()
    labels = labels.clone().detach()
    
    # 前向传播
    outputs = model(node_features, adj_matrix)
    loss = F.cross_entropy(outputs, labels)
    
    # 计算梯度
    model.zero_grad()
    loss.backward()
    
    # 检查梯度是否存在
    if node_features.grad is None:
        print("警告: 输入特征没有梯度，使用随机扰动")
        perturbation = torch.randn_like(node_features) * epsilon
    else:
        # 生成对抗扰动
        perturbation = epsilon * node_features.grad.sign()
    
    # 生成对抗样本
    node_features_adv = node_features.detach() + perturbation
    node_features_adv = torch.clamp(node_features_adv, -1, 1)
    
    return node_features_adv

def generate_pgd_attack(model, node_features, adj_matrix, labels, epsilon=0.1, alpha=0.01, num_iter=10):
    """使用PGD生成对抗样本"""
    model.eval()
    
    # 确保所有输入都需要梯度
    node_features = node_features.clone().detach().requires_grad_(True)
    adj_matrix = adj_matrix.clone().detach()
    labels = labels.clone().detach()
    
    # 初始化对抗样本
    node_features_adv = node_features.clone().detach()
    
    # 随机初始化
    node_features_adv = node_features_adv + torch.randn_like(node_features_adv) * epsilon
    
    for _ in range(num_iter):
        node_features_adv.requires_grad_(True)
        
        # 前向传播
        outputs = model(node_features_adv, adj_matrix)
        loss = F.cross_entropy(outputs, labels)
        
        # 计算梯度
        model.zero_grad()
        loss.backward()
        
        # 检查梯度是否存在
        if node_features_adv.grad is None:
            print("警告: 输入特征没有梯度，使用随机扰动")
            perturbation = torch.randn_like(node_features_adv) * alpha
        else:
            # 更新对抗样本
            perturbation = alpha * node_features_adv.grad.sign()
        
        with torch.no_grad():
            node_features_adv = node_features_adv + perturbation
            
            # 投影到epsilon球内
            delta = node_features_adv - node_features
            delta = torch.clamp(delta, -epsilon, epsilon)
            node_features_adv = torch.clamp(node_features + delta, -1, 1)
    
    return node_features_adv

def evaluate_adversarial_robustness(model, test_loader, device, epsilon_range=[0.01, 0.05, 0.1, 0.2]):
    """评估模型在对抗样本上的鲁棒性"""
    print("\n开始对抗鲁棒性评估...")
    model.eval()
    
    results = {
        'fgsm': {},
        'pgd': {}
    }
    
    # 原始测试
    original_metrics = test(test_loader, device, model, F.cross_entropy)
    print(f"\n原始测试结果:")
    print(f"F1分数: {original_metrics['f1']*100:.2f}%")
    print(f"准确率: {original_metrics['accuracy']*100:.2f}%")
    print(f"AUC: {original_metrics['auc']*100:.2f}%")
    
    # FGSM攻击评估
    print("\nFGSM攻击评估:")
    for epsilon in epsilon_range:
        print(f"\nepsilon = {epsilon}")
        all_preds = []
        all_labels = []
        all_probs = []
        
        for batch in test_loader:
            node_features, adj_matrix, labels = [x.to(device) for x in batch]
            
            try:
                # 生成FGSM对抗样本
                node_features_adv = generate_fgsm_attack(
                    model, node_features, adj_matrix, labels, epsilon
                )
                
                # 在对抗样本上进行预测
                with torch.no_grad():
                    outputs = model(node_features_adv, adj_matrix)
                    preds = outputs.argmax(dim=1)
                    probs = F.softmax(outputs, dim=1)
                
                all_preds.extend(preds.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
                all_probs.extend(probs.cpu().numpy())
                
            except Exception as e:
                print(f"警告: 处理批次时出错: {str(e)}")
                continue
        
        if not all_preds:
            print(f"警告: epsilon={epsilon} 没有有效的预测结果")
            continue
            
        # 计算指标
        metrics = calculate_metrics(
            np.array(all_labels),
            np.array(all_preds),
            np.array(all_probs)
        )
        
        results['fgsm'][epsilon] = metrics
        print(f"F1分数: {metrics['f1']*100:.2f}%")
        print(f"准确率: {metrics['accuracy']*100:.2f}%")
        print(f"AUC: {metrics['auc']*100:.2f}%")
    
    # PGD攻击评估
    print("\nPGD攻击评估:")
    for epsilon in epsilon_range:
        print(f"\nepsilon = {epsilon}")
        all_preds = []
        all_labels = []
        all_probs = []
        
        for batch in test_loader:
            node_features, adj_matrix, labels = [x.to(device) for x in batch]
            
            try:
                # 生成PGD对抗样本
                node_features_adv = generate_pgd_attack(
                    model, node_features, adj_matrix, labels, epsilon
                )
                
                # 在对抗样本上进行预测
                with torch.no_grad():
                    outputs = model(node_features_adv, adj_matrix)
                    preds = outputs.argmax(dim=1)
                    probs = F.softmax(outputs, dim=1)
                
                all_preds.extend(preds.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
                all_probs.extend(probs.cpu().numpy())
                
            except Exception as e:
                print(f"警告: 处理批次时出错: {str(e)}")
                continue
        
        if not all_preds:
            print(f"警告: epsilon={epsilon} 没有有效的预测结果")
            continue
            
        # 计算指标
        metrics = calculate_metrics(
            np.array(all_labels),
            np.array(all_preds),
            np.array(all_probs)
        )
        
        results['pgd'][epsilon] = metrics
        print(f"F1分数: {metrics['f1']*100:.2f}%")
        print(f"准确率: {metrics['accuracy']*100:.2f}%")
        print(f"AUC: {metrics['auc']*100:.2f}%")
    
    return results

def main():
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 设置随机种子
    set_seed(42)
    
    # 创建结果保存目录
    results_dir = './results'
    os.makedirs(results_dir, exist_ok=True)
    
    # 创建结果文件
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_file = os.path.join(results_dir, f'training_results_{timestamp}.txt')
    
    # 写入训练开始信息
    with open(results_file, 'w', encoding='utf-8') as f:
        f.write(f"训练开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"使用设备: {device}\n\n")
    
    # 加载处理好的数据
    print("加载处理好的数据...")
    train_data = load_processed_data('./processed_data/train_processed.jsonl')
    valid_data = load_processed_data('./processed_data/valid_processed.jsonl')
    test_data = load_processed_data('./processed_data/test_processed.jsonl')
    
    # 合并训练集和验证集用于交叉验证
    train_valid_data = train_data + valid_data
    
    # 执行k-fold交叉验证
    mean_f1, std_f1 = k_fold_cross_validation(train_valid_data, device, n_splits=5)
    
    # 记录交叉验证结果
    with open(results_file, 'a', encoding='utf-8') as f:
        f.write("\n交叉验证结果:\n")
        f.write(f"平均F1分数: {mean_f1*100:.2f}% ± {std_f1*100:.2f}%\n")
    
    # 创建测试数据集
    test_dataset = VulDataset(test_data, max_seq_len=512, augment=False)
    test_loader = DataLoader(
        test_dataset,
        batch_size=128,
        shuffle=False,
        num_workers=8,
        collate_fn=collate_fn,
        pin_memory=True
    )
    
    # 创建模型
    model = VulModel(
        input_dim=14,
        graph_dim=32,
        hidden_dim=128,
        num_classes=2
    ).to(device)
    
    # 加载最佳模型
    print("\n加载最佳模型进行测试...")
    checkpoint = torch.load('./checkpoints/best_model.pth')
    model.load_state_dict(checkpoint['model_state_dict'])
    
    # 测试原始数据
    print("\n测试原始数据...")
    criterion = FocalLoss(alpha=0.75, gamma=2.0)
    test_metrics = test(test_loader, device, model, criterion)
    print(f"测试指标: {test_metrics}")
    
    # 记录测试结果
    with open(results_file, 'a', encoding='utf-8') as f:
        f.write("\n最终测试结果:\n")
        f.write(f"测试指标: {test_metrics}\n")
    
    # 鲁棒性分析
    print("\n开始鲁棒性分析...")
    
    # 1. 对抗样本测试
    print("\n1. 对抗样本测试")
    adv_metrics = test_adversarial_samples(model, test_data, device)
    
    # 2. 噪声测试
    print("\n2. 噪声测试")
    noise_levels = [0.01, 0.05, 0.1, 0.2]
    noise_metrics = {}
    for noise_level in noise_levels:
        print(f"测试噪声级别: {noise_level}")
        noisy_metrics = test_with_noise(model, test_loader, device, noise_level)
        noise_metrics[noise_level] = noisy_metrics
    
    # 3. 特征扰动测试
    print("\n3. 特征扰动测试")
    perturbation_levels = [0.1, 0.2, 0.3]
    perturbation_metrics = {}
    for pert_level in perturbation_levels:
        print(f"测试扰动级别: {pert_level}")
        pert_metrics = test_with_perturbation(model, test_loader, device, pert_level)
        perturbation_metrics[pert_level] = pert_metrics
    
    # 4. 对抗鲁棒性评估
    print("\n4. 对抗鲁棒性评估")
    adversarial_results = evaluate_adversarial_robustness(
        model,
        test_loader,
        device,
        epsilon_range=[0.01, 0.05, 0.1, 0.2]
    )
    
    # 记录鲁棒性分析结果
    with open(results_file, 'a', encoding='utf-8') as f:
        f.write("\n模型鲁棒性分析:\n")
        f.write("\n1. 对抗样本测试结果:\n")
        f.write(f"原始F1分数: {test_metrics['f1']*100:.2f}%\n")
        f.write(f"对抗F1分数: {adv_metrics['f1']*100:.2f}%\n")
        f.write(f"性能下降: {(test_metrics['f1'] - adv_metrics['f1'])*100:.2f}%\n")
        
        f.write("\n2. 噪声测试结果:\n")
        for noise_level, metrics in noise_metrics.items():
            f.write(f"\n噪声级别 {noise_level}:\n")
            f.write(f"F1分数: {metrics['f1']*100:.2f}%\n")
            f.write(f"准确率: {metrics['accuracy']*100:.2f}%\n")
            f.write(f"精确率: {metrics['precision']*100:.2f}%\n")
            f.write(f"召回率: {metrics['recall']*100:.2f}%\n")
        
        f.write("\n3. 特征扰动测试结果:\n")
        for pert_level, metrics in perturbation_metrics.items():
            f.write(f"\n扰动级别 {pert_level}:\n")
            f.write(f"F1分数: {metrics['f1']*100:.2f}%\n")
            f.write(f"准确率: {metrics['accuracy']*100:.2f}%\n")
            f.write(f"精确率: {metrics['precision']*100:.2f}%\n")
            f.write(f"召回率: {metrics['recall']*100:.2f}%\n")
        
        f.write("\n4. 对抗鲁棒性评估结果:\n")
        f.write("\nFGSM攻击结果:\n")
        for epsilon, metrics in adversarial_results['fgsm'].items():
            f.write(f"\nepsilon = {epsilon}:\n")
            f.write(f"F1分数: {metrics['f1']*100:.2f}%\n")
            f.write(f"准确率: {metrics['accuracy']*100:.2f}%\n")
            f.write(f"AUC: {metrics['auc']*100:.2f}%\n")
        
        f.write("\nPGD攻击结果:\n")
        for epsilon, metrics in adversarial_results['pgd'].items():
            f.write(f"\nepsilon = {epsilon}:\n")
            f.write(f"F1分数: {metrics['f1']*100:.2f}%\n")
            f.write(f"准确率: {metrics['accuracy']*100:.2f}%\n")
            f.write(f"AUC: {metrics['auc']*100:.2f}%\n")
    
    # 可视化鲁棒性分析结果
    visualize_robustness_results(
        test_metrics,
        adv_metrics,
        noise_metrics,
        perturbation_metrics,
        save_dir='./visualizations'
    )
    
    # 记录训练结束时间
    with open(results_file, 'a', encoding='utf-8') as f:
        f.write(f"\n训练结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
    
    print(f"\n训练结果已保存到: {results_file}")

if __name__ == "__main__":
    main() 