import numpy as np
import json
from collections import Counter
import warnings
import re
from datetime import datetime
import multiprocessing
from concurrent.futures import Process<PERSON>oolExecutor
from tqdm import tqdm
import torch
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import os
import logging
import sys
import math

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# 忽略特定警告
warnings.filterwarnings("ignore", category=UserWarning)

def load_jsonl_data(jsonl_file):
    """
    加载JSONL格式的源文件
    Args:
        jsonl_file: JSONL文件路径
    Returns:
        加载的数据列表
    """
    try:
        if not os.path.exists(jsonl_file):
            logger.error(f"文件不存在: {jsonl_file}")
            return []
            
        data = []
        with open(jsonl_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                if line.strip():  # 跳过空行
                    try:
                        item = json.loads(line)
                        if not isinstance(item, dict):
                            logger.warning(f"第{line_num}行数据格式错误: 不是字典类型")
                            continue
                        if 'nodes_codes' not in item or 'target' not in item:
                            logger.warning(f"第{line_num}行数据缺少必要字段")
                            continue
                        # 验证数据格式
                        if not isinstance(item['nodes_codes'], list):
                            logger.warning(f"第{line_num}行nodes_codes不是列表类型")
                            continue
                        if not isinstance(item['target'], (int, float)):
                            logger.warning(f"第{line_num}行target不是数值类型")
                            continue
                        # 确保nodes_codes不为空
                        if not item['nodes_codes']:
                            logger.warning(f"第{line_num}行nodes_codes为空")
                            continue
                        data.append(item)
                    except json.JSONDecodeError as e:
                        logger.warning(f"第{line_num}行JSON解析错误: {str(e)}")
                        continue
        logger.info(f"成功加载文件 {jsonl_file}: {len(data)} 条数据")
        return data
    except Exception as e:
        logger.error(f"加载JSONL文件时出错: {str(e)}")
        return []

def extract_code_features(code):
    """提取代码特征，包含自适应特征处理"""
    try:
        features = []
        
        # 1. 基本统计特征 - 使用更高效的方法
        code_len = len(code)
        code_words = len(code.split())
        code_lines = len(code.splitlines())
        
        # 使用位运算优化特征计算
        has_numbers = any(c.isdigit() for c in code)
        has_special = any(not c.isalnum() for c in code)
        
        # 2. 使用Counter优化计数
        char_counter = Counter(code)
        bracket_count = char_counter['{'] + char_counter['['] + char_counter['(']
        
        # 3. 使用集合操作优化关键字匹配
        keywords = {'if', 'else', 'for', 'while', 'switch', 'case', 'break', 'continue', 'return'}
        code_words_set = set(code.split())
        keyword_count = len(keywords & code_words_set)
        
        # 4. 使用位运算优化操作符计数
        operators = {'+', '-', '*', '/', '=', '>', '<', '!'}
        operator_count = sum(char_counter[op] for op in operators)
        
        # 5. 使用更高效的正则表达式
        var_count = len(re.findall(r'\b[a-zA-Z_]\w*\b', code))
        func_count = len(re.findall(r'\b[a-zA-Z_]\w*\s*\(', code))
        
        # 6. 使用更高效的注释计数
        comment_count = sum(1 for line in code.splitlines() if line.strip().startswith(('//', '/*', '*', '*/')))
        
        # 7. 计算代码复杂度
        complexity = min((bracket_count + keyword_count + operator_count) / 50.0, 1.0)
        
        # 8. 特征归一化和异常值处理
        def normalize_feature(value, max_value, min_value=0):
            """归一化特征值，处理异常值"""
            if value < min_value:
                return 0.0
            if value > max_value:
                return 1.0
            return (value - min_value) / (max_value - min_value)
        
        # 9. 自适应特征处理
        # 代码长度 - 使用对数缩放处理长尾分布
        code_len_norm = normalize_feature(math.log1p(code_len), math.log1p(1000))
        
        # 词数 - 使用对数缩放
        code_words_norm = normalize_feature(math.log1p(code_words), math.log1p(100))
        
        # 行数 - 使用对数缩放
        code_lines_norm = normalize_feature(math.log1p(code_lines), math.log1p(50))
        
        # 变量计数 - 使用对数缩放处理分散分布
        var_count_norm = normalize_feature(math.log1p(var_count), math.log1p(50))
        
        # 代码复杂度 - 使用对数缩放处理分散分布
        complexity_norm = normalize_feature(math.log1p(complexity * 50), math.log1p(50))
        
        # 10. 特征组合
        # 计算特征之间的交互
        code_density = code_words / (code_len + 1e-6)  # 代码密度
        var_density = var_count / (code_words + 1e-6)  # 变量密度
        complexity_density = complexity / (code_lines + 1e-6)  # 复杂度密度
        
        # 归一化特征
        features.extend([
            code_len_norm,                # 1. 代码长度
            code_words_norm,              # 2. 词数
            code_lines_norm,              # 3. 行数
            float(has_numbers),           # 4. 是否包含数字
            float(has_special),           # 5. 是否包含特殊字符
            normalize_feature(bracket_count, 20),  # 6. 括号计数
            normalize_feature(keyword_count, 10),  # 7. 关键字计数
            var_count_norm,               # 8. 变量计数
            normalize_feature(func_count, 20),     # 9. 函数计数
            normalize_feature(comment_count, 10),  # 10. 注释计数
            complexity_norm,              # 11. 代码复杂度
            normalize_feature(code_density, 1.0),  # 12. 代码密度
            normalize_feature(var_density, 1.0),   # 13. 变量密度
            normalize_feature(complexity_density, 1.0)  # 14. 复杂度密度
        ])
        
        # 11. 特征平滑处理
        features = np.array(features, dtype=np.float32)
        # 使用移动平均平滑处理
        window_size = 3
        features = np.convolve(features, np.ones(window_size)/window_size, mode='same')
        
        # 12. 特征范围限制
        features = np.clip(features, 0.0, 1.0)
        
        return features.tolist()
        
    except Exception as e:
        logger.warning(f"特征提取出错: {str(e)}")
        return None

def clean_and_deduplicate_data(data):
    """清洗和去重数据"""
    print("\n开始清洗和去重数据...")
    
    # 使用字典存储唯一的代码
    unique_codes = {}
    duplicates = 0
    
    # 第一次去重：基于规范化后的代码
    for item in data:
        code = normalize_code("".join(item.get('nodes_codes', [])))
        if code:  # 忽略空代码
            if code not in unique_codes:
                unique_codes[code] = item
            else:
                duplicates += 1
    
    # 转换回列表
    unique_data = list(unique_codes.values())
    
    print(f"第一次去重结果:")
    print(f"- 原始样本数: {len(data)}")
    print(f"- 去重后样本数: {len(unique_data)}")
    print(f"- 重复样本数: {duplicates}")
    
    # 第二次去重：基于代码内容和标签的组合
    unique_codes = {}
    duplicates = 0
    
    for item in unique_data:
        code = normalize_code("".join(item.get('nodes_codes', [])))
        label = item['target']
        key = f"{code}_{label}"  # 使用代码和标签的组合作为键
        
        if key not in unique_codes:
            unique_codes[key] = item
        else:
            duplicates += 1
    
    # 转换回列表
    final_data = list(unique_codes.values())
    
    print(f"第二次去重结果:")
    print(f"- 输入样本数: {len(unique_data)}")
    print(f"- 去重后样本数: {len(final_data)}")
    print(f"- 重复样本数: {duplicates}")
    
    return final_data

def normalize_code(code):
    """规范化代码，去除空白字符和注释"""
    if not isinstance(code, str):
        return ""
    
    # 移除注释
    code = re.sub(r'//.*?$|/\*.*?\*/', '', code, flags=re.MULTILINE)
    
    # 移除空白字符
    code = re.sub(r'\s+', '', code)
    
    # 移除字符串内容
    code = re.sub(r'"[^"]*"', '""', code)
    code = re.sub(r"'[^']*'", "''", code)
    
    return code

def calculate_code_similarity(code1, code2):
    """计算两个代码片段的相似度，使用更高效的方法"""
    # 规范化代码
    code1 = normalize_code(code1)
    code2 = normalize_code(code2)
    
    if not code1 or not code2:
        return 0.0
    
    # 使用更高效的相似度计算方法
    # 1. 计算代码长度比例
    len_ratio = min(len(code1), len(code2)) / max(len(code1), len(code2))
    if len_ratio < 0.5:  # 如果长度差异太大，直接返回低相似度
        return 0.0
    
    # 2. 计算字符重叠率
    set1 = set(code1)
    set2 = set(code2)
    overlap = len(set1.intersection(set2))
    total = len(set1.union(set2))
    char_similarity = overlap / total if total > 0 else 0.0
    
    # 3. 如果字符相似度已经很低，直接返回
    if char_similarity < 0.3:
        return char_similarity
    
    # 4. 计算关键字符序列的相似度
    def get_key_chars(code):
        # 只保留关键字符
        return ''.join(c for c in code if c in '{}[]()<>;=+-*/&|!')
    
    key1 = get_key_chars(code1)
    key2 = get_key_chars(code2)
    key_similarity = len(set(key1).intersection(set(key2))) / len(set(key1).union(set(key2))) if key1 or key2 else 0.0
    
    # 综合相似度
    return 0.6 * char_similarity + 0.4 * key_similarity

def get_code_hash(code):
    """计算代码的哈希值，用于快速比较"""
    # 规范化代码
    code = normalize_code(code)
    if not code:
        return ""
    
    # 提取关键特征
    key_chars = ''.join(c for c in code if c in '{}[]()<>;=+-*/&|!')
    # 计算前100个字符的哈希值
    return hash(code[:100] + key_chars[:50])

def get_code_signature(code):
    """获取代码的轻量级签名"""
    if not isinstance(code, str):
        return ""
    
    # 1. 移除注释和空白
    code = re.sub(r'//.*?$|/\*.*?\*/', '', code, flags=re.MULTILINE)
    code = re.sub(r'\s+', '', code)
    
    # 2. 提取关键特征
    # 只保留关键字符和数字
    key_chars = ''.join(c for c in code if c in '{}[]()<>;=+-*/&|!0123456789')
    
    # 3. 计算特征统计
    char_count = len(key_chars)
    if char_count == 0:
        return ""
    
    # 4. 生成签名
    # 使用前50个字符和字符统计信息
    return f"{key_chars[:50]}_{char_count}"

def check_batch_similarity(args):
    """检查一批样本的相似度"""
    batch, test_signatures = args
    similar_count = 0
    similar_samples = []
    
    for item in batch:
        code = item['normalized_code']
        if len(code) < 10:  # 忽略太短的代码
            continue
            
        # 计算当前代码的签名
        signature = get_code_signature(code)
        if not signature:
            continue
        
        # 快速签名匹配
        if signature in test_signatures:
            similar_count += 1
            similar_samples.append(item)
    
    return similar_count, similar_samples

class CodeDataset(Dataset):
    def __init__(self, data):
        self.data = data
        
    def __len__(self):
        return len(self.data)
        
    def __getitem__(self, idx):
        return self.data[idx]

def custom_collate_fn(batch):
    """自定义的collate函数，处理不同大小的数据"""
    return batch

def get_code_tensor(code, max_length=100):
    """将代码转换为张量表示"""
    if not isinstance(code, str):
        return torch.zeros(max_length, dtype=torch.long)
    
    # 规范化代码
    code = normalize_code(code)
    
    # 提取关键特征
    # 1. 保留关键字和操作符
    keywords = {'if', 'else', 'for', 'while', 'switch', 'case', 'break', 'continue', 'return', 'int', 'void', 'char', 'float', 'double', 'struct', 'class', 'public', 'private', 'protected'}
    operators = {'+', '-', '*', '/', '=', '>', '<', '!', '&', '|', '^', '%', '++', '--', '+=', '-=', '*=', '/=', '==', '!=', '>=', '<=', '&&', '||'}
    
    # 2. 提取代码结构特征
    tokens = []
    for token in code.split():
        if token in keywords:
            tokens.append(token)
        elif token in operators:
            tokens.append(token)
        elif token in '{}[]()<>;,':
            tokens.append(token)
    
    # 3. 转换为数字序列
    token_to_id = {token: i+1 for i, token in enumerate(set(tokens))}
    ids = [token_to_id.get(token, 0) for token in tokens[:max_length]]
    if len(ids) < max_length:
        ids.extend([0] * (max_length - len(ids)))
    
    return torch.tensor(ids, dtype=torch.long)

def check_similarity_gpu(train_data, test_data, batch_size=512, similarity_threshold=0.95):
    """使用GPU检查代码相似度"""
    # 检查是否有可用的GPU
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 准备测试集数据
    print("准备测试集数据...")
    test_codes = [item['normalized_code'] for item in test_data]
    test_tensors = torch.stack([get_code_tensor(code) for code in test_codes]).to(device)
    
    # 创建数据加载器
    train_dataset = CodeDataset(train_data)
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=False,
        collate_fn=custom_collate_fn,
        num_workers=0
    )
    
    similar_count = 0
    similar_samples = []
    
    # 使用tqdm显示进度
    with tqdm(total=len(train_loader), desc="GPU检查相似度") as pbar:
        for batch in train_loader:
            # 准备批次数据
            batch_codes = [item['normalized_code'] for item in batch]
            batch_tensors = torch.stack([get_code_tensor(code) for code in batch_codes]).to(device)
            
            # 计算相似度矩阵
            # 使用余弦相似度
            batch_tensors = F.normalize(batch_tensors.float(), p=2, dim=1)
            test_tensors = F.normalize(test_tensors.float(), p=2, dim=1)
            similarity = torch.mm(batch_tensors, test_tensors.t())
            
            # 找出相似度超过阈值的样本
            similar_mask = (similarity > similarity_threshold).any(dim=1)
            similar_indices = torch.where(similar_mask)[0]
            
            # 更新计数和样本列表
            similar_count += len(similar_indices)
            similar_samples.extend([batch[i] for i in similar_indices])
            
            # 清理GPU内存
            del batch_tensors, similarity, similar_mask
            torch.cuda.empty_cache()
            
            pbar.update(1)
    
    return similar_count, similar_samples

def check_duplicates(train_data, test_data):
    """检查训练集和测试集之间是否有完全相同的代码"""
    print("\n检查代码重复...")
    
    # 创建训练集代码的集合
    train_codes = set()
    for item in train_data:
        code = normalize_code("".join(item.get('nodes_codes', [])))
        if code:  # 忽略空代码
            train_codes.add(code)
    
    # 检查测试集中的代码
    duplicate_count = 0
    duplicate_samples = []
    
    for item in test_data:
        code = normalize_code("".join(item.get('nodes_codes', [])))
        if code and code in train_codes:
            duplicate_count += 1
            duplicate_samples.append(item)
    
    return duplicate_count, duplicate_samples

def remove_duplicates(data):
    """移除数据集中的重复代码"""
    print("\n移除重复代码...")
    
    # 使用字典存储唯一的代码
    unique_codes = {}
    duplicates = 0
    
    for item in data:
        code = normalize_code("".join(item.get('nodes_codes', [])))
        if code:  # 忽略空代码
            if code not in unique_codes:
                unique_codes[code] = item
            else:
                duplicates += 1
    
    # 转换回列表
    unique_data = list(unique_codes.values())
    
    print(f"去重结果:")
    print(f"- 原始样本数: {len(data)}")
    print(f"- 去重后样本数: {len(unique_data)}")
    print(f"- 重复样本数: {duplicates}")
    
    return unique_data

def split_data(data, train_ratio=0.8, valid_ratio=0.1, test_ratio=0.1, random_state=42):
    """划分数据集"""
    print("\n开始划分数据集...")
    
    # 确保比例和为1
    assert abs(train_ratio + valid_ratio + test_ratio - 1.0) < 1e-6, "比例之和必须为1"
    
    # 按标签分组
    label_groups = {}
    for item in data:
        label = item['target']
        if label not in label_groups:
            label_groups[label] = []
        label_groups[label].append(item)
    
    # 划分数据集
    train_data = []
    valid_data = []
    test_data = []
    
    # 对每个标签组进行划分
    for label, items in label_groups.items():
        # 随机打乱
        np.random.seed(random_state)
        np.random.shuffle(items)
        
        # 计算每个集合的大小
        n = len(items)
        n_train = int(n * train_ratio)
        n_valid = int(n * valid_ratio)
        
        # 划分数据
        train_data.extend(items[:n_train])
        valid_data.extend(items[n_train:n_train + n_valid])
        test_data.extend(items[n_train + n_valid:])
    
    # 再次随机打乱每个集合
    np.random.seed(random_state)
    np.random.shuffle(train_data)
    np.random.shuffle(valid_data)
    np.random.shuffle(test_data)
    
    print(f"数据集划分完成:")
    print(f"- 训练集: {len(train_data)} 样本")
    print(f"- 验证集: {len(valid_data)} 样本")
    print(f"- 测试集: {len(test_data)} 样本")
    
    # 检查标签分布
    for name, dataset in [("训练集", train_data), ("验证集", valid_data), ("测试集", test_data)]:
        labels = [item['target'] for item in dataset]
        label_counts = np.bincount(labels)
        print(f"\n{name}标签分布:")
        print(f"- 类别0: {label_counts[0]} 样本")
        print(f"- 类别1: {label_counts[1]} 样本")
        print(f"- 正样本比例: {label_counts[1]/len(labels)*100:.2f}%")
    
    return train_data, valid_data, test_data

def check_data_leakage(train_data, valid_data, test_data):
    """检查数据泄漏和样本重复"""
    print("\n检查数据泄漏和样本重复...")
    
    # 收集所有样本的唯一标识
    def get_sample_signature(item):
        # 使用代码内容和标签作为唯一标识
        code_signature = "".join(item.get('nodes_codes', []))
        return f"{code_signature}_{item.get('target', '')}"
    
    # 收集所有数据集的签名
    train_signatures = {get_sample_signature(item) for item in train_data}
    valid_signatures = {get_sample_signature(item) for item in valid_data}
    test_signatures = {get_sample_signature(item) for item in test_data}
    
    # 检查训练集和验证集的重叠
    train_valid_overlap = train_signatures.intersection(valid_signatures)
    if train_valid_overlap:
        print(f"警告：发现{len(train_valid_overlap)}个样本在训练集和验证集中重复")
        print("示例重复样本:")
        for sig in list(train_valid_overlap)[:3]:  # 只显示前3个示例
            print(f"- {sig[:100]}...")  # 只显示签名的前100个字符
    
    # 检查训练集和测试集的重叠
    train_test_overlap = train_signatures.intersection(test_signatures)
    if train_test_overlap:
        print(f"警告：发现{len(train_test_overlap)}个样本在训练集和测试集中重复")
        print("示例重复样本:")
        for sig in list(train_test_overlap)[:3]:
            print(f"- {sig[:100]}...")
    
    # 检查验证集和测试集的重叠
    valid_test_overlap = valid_signatures.intersection(test_signatures)
    if valid_test_overlap:
        print(f"警告：发现{len(valid_test_overlap)}个样本在验证集和测试集中重复")
        print("示例重复样本:")
        for sig in list(valid_test_overlap)[:3]:
            print(f"- {sig[:100]}...")
    
    # 检查每个数据集内部的重复
    def check_internal_duplicates(data, name):
        signatures = [get_sample_signature(item) for item in data]
        unique_signatures = set(signatures)
        if len(signatures) != len(unique_signatures):
            duplicates = len(signatures) - len(unique_signatures)
            print(f"警告：{name}中存在{duplicates}个重复样本")
            # 找出重复的样本
            from collections import Counter
            duplicate_counts = Counter(signatures)
            duplicates = {sig: count for sig, count in duplicate_counts.items() if count > 1}
            print("重复样本示例:")
            for sig, count in list(duplicates.items())[:3]:
                print(f"- 出现{count}次: {sig[:100]}...")
    
    check_internal_duplicates(train_data, "训练集")
    check_internal_duplicates(valid_data, "验证集")
    check_internal_duplicates(test_data, "测试集")
    
    # 检查测试集的独立性
    print("\n检查测试集的独立性:")
    # 使用更高效的相似度计算方法
    def calculate_similarity(sig1, sig2):
        # 使用简单的字符重叠率
        set1 = set(sig1)
        set2 = set(sig2)
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        return intersection / union if union > 0 else 0
    
    # 对测试集进行采样检查
    sample_size = min(100, len(test_data))  # 最多检查100个样本
    test_samples = np.random.choice(test_data, sample_size, replace=False)
    
    similar_samples = 0
    for test_item in test_samples:
        test_sig = get_sample_signature(test_item)
        is_similar = False
        
        # 检查与训练集的相似度
        for train_item in train_data:
            train_sig = get_sample_signature(train_item)
            if calculate_similarity(test_sig, train_sig) > 0.8:  # 相似度阈值
                is_similar = True
                break
        
        # 如果与训练集不相似，检查与验证集的相似度
        if not is_similar:
            for valid_item in valid_data:
                valid_sig = get_sample_signature(valid_item)
                if calculate_similarity(test_sig, valid_sig) > 0.8:
                    is_similar = True
                    break
        
        if is_similar:
            similar_samples += 1
    
    if similar_samples > 0:
        print(f"警告：在{len(test_samples)}个测试样本中发现{similar_samples}个与训练集或验证集高度相似的样本")
        print(f"估计总体相似样本比例: {similar_samples/len(test_samples)*100:.2f}%")
    else:
        print("测试集完全独立，未发现与训练集或验证集相似的样本")
    
    # 如果发现任何重叠或相似样本，返回False
    has_overlap = len(train_valid_overlap) > 0 or len(train_test_overlap) > 0 or len(valid_test_overlap) > 0
    has_similar = similar_samples > 0
    
    if has_overlap or has_similar:
        print("\n警告：发现数据重叠或相似样本，建议重新划分数据集")
        return False
    
    return True

def extract_code_features_gpu_batch(codes, device, batch_size=256):
    """批量使用GPU提取代码特征"""
    try:
        features = []
        total_batches = (len(codes) + batch_size - 1) // batch_size
        
        with tqdm(total=total_batches, desc="批量提取特征") as pbar:
            for i in range(0, len(codes), batch_size):
                try:
                    batch_codes = codes[i:i + batch_size]
                    
                    # 将代码转换为张量
                    max_len = max(len(code) for code in batch_codes)
                    code_tensors = torch.zeros((len(batch_codes), max_len), dtype=torch.float32, device=device)
                    
                    for j, code in enumerate(batch_codes):
                        code_tensors[j, :len(code)] = torch.tensor([ord(c) for c in code], dtype=torch.float32, device=device)
                    
                    # 1. 基本统计特征
                    code_lens = torch.tensor([len(code) for code in batch_codes], device=device)
                    code_words = torch.tensor([len(code.split()) for code in batch_codes], device=device)
                    code_lines = torch.tensor([len(code.splitlines()) for code in batch_codes], device=device)
                    
                    # 2. 使用GPU计算字符统计
                    unique_chars = torch.unique(code_tensors)
                    char_counts = torch.bincount(code_tensors.long().flatten(), minlength=128)
                    
                    # 3. 计算括号和操作符
                    brackets = torch.tensor([ord(c) for c in '{}[]()'], device=device)
                    operators = torch.tensor([ord(c) for c in '+-*/=><!&|'], device=device)
                    
                    bracket_counts = torch.sum(torch.isin(code_tensors, brackets), dim=1)
                    operator_counts = torch.sum(torch.isin(code_tensors, operators), dim=1)
                    
                    # 4. 计算关键字和变量
                    keywords = {'if', 'else', 'for', 'while', 'switch', 'case', 'break', 'continue', 'return'}
                    keyword_counts = torch.tensor([
                        len(keywords & set(code.split()))
                        for code in batch_codes
                    ], device=device)
                    
                    # 5. 使用GPU计算变量和函数
                    var_pattern = torch.tensor([ord(c) for c in 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ_'], device=device)
                    var_counts = torch.sum(torch.isin(code_tensors, var_pattern), dim=1)
                    
                    # 6. 计算注释
                    comment_counts = torch.tensor([
                        sum(1 for line in code.splitlines() if line.strip().startswith(('//', '/*', '*', '*/')))
                        for code in batch_codes
                    ], device=device)
                    
                    # 7. 计算代码复杂度
                    complexities = torch.minimum(
                        (bracket_counts + keyword_counts + operator_counts) / 50.0,
                        torch.ones_like(bracket_counts)
                    )
                    
                    # 归一化特征
                    batch_features = torch.stack([
                        torch.minimum(code_lens / 1000.0, torch.ones_like(code_lens)),  # 1. 代码长度
                        torch.minimum(code_words / 100.0, torch.ones_like(code_words)),  # 2. 词数
                        torch.minimum(code_lines / 50.0, torch.ones_like(code_lines)),  # 3. 行数
                        torch.tensor([any(c.isdigit() for c in code) for code in batch_codes], device=device).float(),  # 4. 是否包含数字
                        torch.tensor([any(not c.isalnum() for c in code) for code in batch_codes], device=device).float(),  # 5. 是否包含特殊字符
                        torch.minimum(bracket_counts / 20.0, torch.ones_like(bracket_counts)),  # 6. 括号计数
                        torch.minimum(keyword_counts / 10.0, torch.ones_like(keyword_counts)),  # 7. 关键字计数
                        torch.minimum(operator_counts / 30.0, torch.ones_like(operator_counts)),  # 8. 操作符计数
                        torch.minimum(var_counts / 50.0, torch.ones_like(var_counts)),  # 9. 变量计数
                        torch.minimum(comment_counts / 10.0, torch.ones_like(comment_counts)),  # 10. 注释计数
                        complexities,  # 11. 代码复杂度
                        torch.zeros_like(code_lens),  # 12. 预留特征
                        torch.zeros_like(code_lens)   # 13. 预留特征
                    ], dim=1)
                    
                    features.append(batch_features)
                    
                    # 清理GPU内存
                    del code_tensors, code_lens, code_words, code_lines, unique_chars, char_counts
                    del bracket_counts, operator_counts, keyword_counts, var_counts, comment_counts, complexities
                    torch.cuda.empty_cache()
                    
                    pbar.update(1)
                    
                except Exception as e:
                    logger.warning(f"处理批次 {i//batch_size + 1}/{total_batches} 时出错: {str(e)}")
                    continue
        
        if not features:
            logger.error("所有批次处理都失败了")
            return None
            
        return torch.cat(features, dim=0)
    except Exception as e:
        logger.error(f"GPU批量特征提取出错: {str(e)}")
        return None

def check_feature_distribution(data):
    """使用GPU检查数据特征分布"""
    try:
        logger.info("开始提取特征...")
        
        # 检查GPU是否可用
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"使用设备: {device}")
        
        # 准备数据
        codes = []
        empty_samples = 0
        total_samples = 0
        
        # 收集所有代码
        for item in tqdm(data, desc="收集代码"):
            total_samples += 1
            if 'nodes_codes' in item and item['nodes_codes']:
                for code in item['nodes_codes']:
                    if code and isinstance(code, str):
                        codes.append(code)
            else:
                empty_samples += 1
        
        if not codes:
            logger.warning("未能收集到任何有效代码")
            return
        
        logger.info(f"收集到 {len(codes)} 个有效代码")
        
        # 批量提取特征
        logger.info("开始批量提取特征...")
        features_tensor = extract_code_features_gpu_batch(codes, device)
        
        if features_tensor is None:
            logger.error("特征提取失败")
            return
        
        logger.info("特征提取完成，开始计算统计信息...")
        
        # 使用GPU计算统计信息
        mean = torch.mean(features_tensor, dim=0)
        std = torch.std(features_tensor, dim=0)
        min_val = torch.min(features_tensor, dim=0)[0]
        max_val = torch.max(features_tensor, dim=0)[0]
        median = torch.median(features_tensor, dim=0)[0]
        
        # 计算异常值
        q1 = torch.quantile(features_tensor, 0.25, dim=0)
        q3 = torch.quantile(features_tensor, 0.75, dim=0)
        iqr = q3 - q1
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr
        outliers = torch.sum((features_tensor < lower_bound) | (features_tensor > upper_bound), dim=0)
        
        # 输出统计信息
        logger.info(f"数据统计:")
        logger.info(f"- 总样本数: {total_samples}")
        logger.info(f"- 空样本数: {empty_samples}")
        logger.info(f"- 有效特征数: {len(codes)}")
        
        logger.info(f"特征统计:")
        logger.info(f"- 均值: {mean.cpu().numpy()}")
        logger.info(f"- 标准差: {std.cpu().numpy()}")
        logger.info(f"- 最小值: {min_val.cpu().numpy()}")
        logger.info(f"- 最大值: {max_val.cpu().numpy()}")
        logger.info(f"- 中位数: {median.cpu().numpy()}")
        logger.info(f"- 异常值数量: {outliers.cpu().numpy()}")
        logger.info(f"- 异常值比例: {(outliers / len(codes) * 100).cpu().numpy()}%")
        
        # 清理GPU内存
        del features_tensor, mean, std, min_val, max_val, median
        del q1, q3, iqr, lower_bound, upper_bound, outliers
        torch.cuda.empty_cache()
        
    except Exception as e:
        logger.error(f"特征分布检查出错: {str(e)}")
        raise

def process_data(train_file, valid_file, test_file, output_dir="./processed_data"):
    """处理数据的主函数"""
    try:
        logger.info("开始处理数据...")
        
        # 检查输入文件
        for file_path in [train_file, valid_file, test_file]:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"输入文件不存在: {file_path}")
            logger.info(f"检查文件 {file_path} 存在")
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        logger.info(f"创建输出目录: {output_dir}")
        
        # 加载数据
        logger.info("加载数据...")
        all_data = []
        for file_path in [train_file, valid_file, test_file]:
            data = load_jsonl_data(file_path)
            if not data:
                logger.warning(f"文件 {file_path} 没有加载到数据")
            else:
                logger.info(f"从 {file_path} 加载了 {len(data)} 条数据")
            all_data.extend(data)
        
        if not all_data:
            raise ValueError("没有加载到任何有效数据")
            
        logger.info(f"成功加载数据: 总样本数 {len(all_data)} 条")
        
        # 数据验证
        logger.info("验证数据格式...")
        valid_data = []
        for item in all_data:
            if not isinstance(item, dict):
                continue
            if 'nodes_codes' not in item or 'target' not in item:
                continue
            if not isinstance(item['nodes_codes'], list) or not item['nodes_codes']:
                continue
            if not isinstance(item['target'], (int, float)):
                continue
            valid_data.append(item)
        
        if not valid_data:
            raise ValueError("验证后没有剩余有效数据")
        
        logger.info(f"数据验证完成: {len(valid_data)} 条有效数据")
        
        # 清洗和去重
        all_data = clean_and_deduplicate_data(valid_data)
        if not all_data:
            raise ValueError("清洗和去重后没有剩余数据")
        
        logger.info(f"清洗和去重后剩余 {len(all_data)} 条数据")
        
        # 划分数据集
        train_data, valid_data, test_data = split_data(
            all_data,
            train_ratio=0.8,
            valid_ratio=0.1,
            test_ratio=0.1,
            random_state=42
        )
        
        if not train_data or not valid_data or not test_data:
            raise ValueError("数据集划分失败")
        
        logger.info(f"数据集划分完成:")
        logger.info(f"- 训练集: {len(train_data)} 样本")
        logger.info(f"- 验证集: {len(valid_data)} 样本")
        logger.info(f"- 测试集: {len(test_data)} 样本")
        
        # 检查特征分布
        logger.info("\n检查特征分布:")
        for name, data in [("训练集", train_data), ("验证集", valid_data), ("测试集", test_data)]:
            logger.info(f"\n{name}特征分布:")
            check_feature_distribution(data)
        
        # 保存处理后的数据
        def save_data(data, filename):
            try:
                output_path = os.path.join(output_dir, filename)
                with open(output_path, 'w', encoding='utf-8') as f:
                    for item in data:
                        f.write(json.dumps(item, ensure_ascii=False) + '\n')
                logger.info(f"成功保存数据到: {output_path}")
            except Exception as e:
                logger.error(f"保存数据到 {filename} 时出错: {str(e)}")
                raise
        
        save_data(train_data, 'train_processed.jsonl')
        save_data(valid_data, 'valid_processed.jsonl')
        save_data(test_data, 'test_processed.jsonl')
        
        logger.info(f"\n处理后的数据已保存到 {output_dir} 目录")
        
        return train_data, valid_data, test_data
        
    except Exception as e:
        logger.error(f"数据处理过程中出错: {str(e)}")
        raise

if __name__ == "__main__":
    try:
        # 设置文件路径
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        train_file = os.path.join(base_dir, "dataset", "train.jsonl")
        valid_file = os.path.join(base_dir, "dataset", "valid.jsonl")
        test_file = os.path.join(base_dir, "dataset", "test.jsonl")
        
        logger.info("程序开始执行...")
        logger.info(f"训练文件: {train_file}")
        logger.info(f"验证文件: {valid_file}")
        logger.info(f"测试文件: {test_file}")
        
        # 处理数据
        train_data, valid_data, test_data = process_data(train_file, valid_file, test_file)
        
        logger.info("数据处理完成")
        
    except Exception as e:
        logger.error(f"程序执行失败: {str(e)}")
        sys.exit(1)