nohup: ignoring input
03/18/2025 15:27:19 - WARNING - __main__ -   Process rank: -1, device: cuda, n_gpu: 1, distributed training: False, 16-bits training: False
Some weights of the model checkpoint at microsoft/graphcodebert-base were not used when initializing RobertaModel: ['lm_head.bias', 'lm_head.layer_norm.bias', 'lm_head.decoder.bias', 'lm_head.dense.weight', 'lm_head.layer_norm.weight', 'lm_head.dense.bias', 'lm_head.decoder.weight']
- This IS expected if you are initializing RobertaModel from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).
- This IS NOT expected if you are initializing RobertaModel from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).
Some weights of RobertaModel were not initialized from the model checkpoint at microsoft/graphcodebert-base and are newly initialized: ['roberta.pooler.dense.bias', 'roberta.pooler.dense.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
03/18/2025 15:27:41 - INFO - __main__ -   Training/evaluation parameters Namespace(adam_epsilon=1e-08, alpha_weight=1.0, att_op='mul', block_size=400, cache_dir='', config_name='', device=device(type='cuda'), do_eval=True, do_lower_case=False, do_test=True, do_train=True, epoch=200, eval_all_checkpoints=False, eval_batch_size=32, eval_data_file='../dataset/NVD/my_valid.jsonl', evaluate_during_training=True, feature_dim_size=768, fp16=False, fp16_opt_level='O1', gnn='ReGCN', gradient_accumulation_steps=1, hidden_size=128, learning_rate=0.0005, local_rank=-1, logging_steps=50, max_grad_norm=1.0, max_steps=-1, mlm=False, mlm_probability=0.15, model='GNNs', model_name_or_path='microsoft/graphcodebert-base', model_type='roberta', n_gpu=1, no_cuda=False, num_GNN_layers=2, num_classes=2, num_train_epochs=1.0, output_dir='./saved_models', overwrite_cache=False, overwrite_output_dir=False, per_gpu_eval_batch_size=32, per_gpu_train_batch_size=32, remove_residual=False, save_steps=50, save_total_limit=None, seed=123456, server_ip='', server_port='', start_epoch=0, start_step=0, test_data_file='../dataset/NVD/my_test.jsonl', tokenizer_name='microsoft/graphcodebert-base', train_batch_size=32, train_data_file='../dataset/NVD/my_train.jsonl', training_percent=1.0, warmup_steps=0, weight_decay=0.0)
03/18/2025 15:37:00 - INFO - __main__ -   ***** Running training *****
03/18/2025 15:37:00 - INFO - __main__ -     Num examples = 29256
03/18/2025 15:37:00 - INFO - __main__ -     Num Epochs = 200
03/18/2025 15:37:00 - INFO - __main__ -     Instantaneous batch size per GPU = 32
03/18/2025 15:37:00 - INFO - __main__ -     Total train batch size (w. parallel, distributed & accumulation) = 32
03/18/2025 15:37:00 - INFO - __main__ -     Gradient Accumulation steps = 1
03/18/2025 15:37:00 - INFO - __main__ -     Total optimization steps = 183000
using default unweighted graph
03/18/2025 16:12:53 - INFO - __main__ -   ***** Running evaluation *****
03/18/2025 16:12:53 - INFO - __main__ -     Num examples = 3657
03/18/2025 16:12:53 - INFO - __main__ -     Batch size = 32
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
03/18/2025 16:17:28 - INFO - __main__ -   epoch 0 loss 0.5887
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
03/23/2025 14:11:31 - INFO - __main__ -   ***** Running evaluation *****
03/23/2025 14:11:31 - INFO - __main__ -     Num examples = 3657
03/23/2025 14:11:31 - INFO - __main__ -     Batch size = 32
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
03/23/2025 14:16:14 - INFO - __main__ -     eval_loss = 0.3732
03/23/2025 14:16:14 - INFO - __main__ -     eval_acc = 0.91
03/23/2025 14:16:14 - INFO - __main__ -     eval_precision = 0.848
03/23/2025 14:16:14 - INFO - __main__ -     eval_recall = 0.791
03/23/2025 14:16:14 - INFO - __main__ -     eval_f1 = 0.8185
03/23/2025 14:16:14 - INFO - __main__ -     eval_auc = 0.871
03/23/2025 14:16:14 - INFO - __main__ -     eval_FPR = 0.0492
03/23/2025 14:16:14 - INFO - __main__ -     eval_FNR = 0.21
03/23/2025 14:16:14 - INFO - __main__ -   epoch 170 loss 0.07656
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
03/23/2025 14:53:07 - INFO - __main__ -   ***** Running evaluation *****
03/23/2025 14:53:07 - INFO - __main__ -     Num examples = 3657
03/23/2025 14:53:07 - INFO - __main__ -     Batch size = 32
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
03/23/2025 14:57:52 - INFO - __main__ -     eval_loss = 0.3755
03/23/2025 14:57:52 - INFO - __main__ -     eval_acc = 0.9078
03/23/2025 14:57:52 - INFO - __main__ -     eval_precision = 0.8388
03/23/2025 14:57:52 - INFO - __main__ -     eval_recall = 0.7932
03/23/2025 14:57:52 - INFO - __main__ -     eval_f1 = 0.8153
03/23/2025 14:57:52 - INFO - __main__ -     eval_auc = 0.8702
03/23/2025 14:57:52 - INFO - __main__ -     eval_FPR = 0.0529
03/23/2025 14:57:52 - INFO - __main__ -     eval_FNR = 0.2079
03/23/2025 14:57:53 - INFO - __main__ -   epoch 171 loss 0.07525
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
03/23/2025 15:33:52 - INFO - __main__ -   ***** Running evaluation *****
03/23/2025 15:33:52 - INFO - __main__ -     Num examples = 3657
03/23/2025 15:33:52 - INFO - __main__ -     Batch size = 32
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
03/23/2025 15:38:26 - INFO - __main__ -     eval_loss = 0.377
03/23/2025 15:38:26 - INFO - __main__ -     eval_acc = 0.913
03/23/2025 15:38:26 - INFO - __main__ -     eval_precision = 0.878
03/23/2025 15:38:26 - INFO - __main__ -     eval_recall = 0.7676
03/23/2025 15:38:26 - INFO - __main__ -     eval_f1 = 0.8191
03/23/2025 15:38:26 - INFO - __main__ -     eval_auc = 0.8653
03/23/2025 15:38:26 - INFO - __main__ -     eval_FPR = 0.0371
03/23/2025 15:38:26 - INFO - __main__ -     eval_FNR = 0.2335
03/23/2025 15:38:27 - INFO - __main__ -   epoch 172 loss 0.07423
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
03/23/2025 16:14:36 - INFO - __main__ -   ***** Running evaluation *****
03/23/2025 16:14:36 - INFO - __main__ -     Num examples = 3657
03/23/2025 16:14:36 - INFO - __main__ -     Batch size = 32
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
03/24/2025 05:22:18 - INFO - __main__ -     eval_loss = 0.3818
03/24/2025 05:22:18 - INFO - __main__ -     eval_acc = 0.9106
03/24/2025 05:22:18 - INFO - __main__ -     eval_precision = 0.8524
03/24/2025 05:22:18 - INFO - __main__ -     eval_recall = 0.7878
03/24/2025 05:22:18 - INFO - __main__ -     eval_f1 = 0.8188
03/24/2025 05:22:18 - INFO - __main__ -     eval_auc = 0.8703
03/24/2025 05:22:18 - INFO - __main__ -     eval_FPR = 0.0474
03/24/2025 05:22:18 - INFO - __main__ -     eval_FNR = 0.2132
03/24/2025 05:22:19 - INFO - __main__ -   epoch 192 loss 0.07119
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
03/24/2025 05:58:36 - INFO - __main__ -   ***** Running evaluation *****
03/24/2025 05:58:36 - INFO - __main__ -     Num examples = 3657
03/24/2025 05:58:36 - INFO - __main__ -     Batch size = 32
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
03/24/2025 06:03:13 - INFO - __main__ -     eval_loss = 0.3812
03/24/2025 06:03:13 - INFO - __main__ -     eval_acc = 0.9106
03/24/2025 06:03:13 - INFO - __main__ -     eval_precision = 0.8468
03/24/2025 06:03:13 - INFO - __main__ -     eval_recall = 0.7953
03/24/2025 06:03:13 - INFO - __main__ -     eval_f1 = 0.8202
03/24/2025 06:03:13 - INFO - __main__ -     eval_auc = 0.8727
03/24/2025 06:03:13 - INFO - __main__ -     eval_FPR = 0.05
03/24/2025 06:03:13 - INFO - __main__ -     eval_FNR = 0.2058
03/24/2025 06:03:13 - INFO - __main__ -   epoch 193 loss 0.0713
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
03/24/2025 06:39:55 - INFO - __main__ -   ***** Running evaluation *****
03/24/2025 06:39:55 - INFO - __main__ -     Num examples = 3657
03/24/2025 06:39:55 - INFO - __main__ -     Batch size = 32
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
03/24/2025 06:44:28 - INFO - __main__ -     eval_loss = 0.38
03/24/2025 06:44:28 - INFO - __main__ -     eval_acc = 0.9081
03/24/2025 06:44:28 - INFO - __main__ -     eval_precision = 0.8382
03/24/2025 06:44:28 - INFO - __main__ -     eval_recall = 0.7953
03/24/2025 06:44:28 - INFO - __main__ -     eval_f1 = 0.8162
03/24/2025 06:44:28 - INFO - __main__ -     eval_auc = 0.8711
03/24/2025 06:44:28 - INFO - __main__ -     eval_FPR = 0.0533
03/24/2025 06:44:28 - INFO - __main__ -     eval_FNR = 0.2058
03/24/2025 06:44:29 - INFO - __main__ -   epoch 194 loss 0.07294
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
03/24/2025 07:20:55 - INFO - __main__ -   ***** Running evaluation *****
03/24/2025 07:20:55 - INFO - __main__ -     Num examples = 3657
03/24/2025 07:20:55 - INFO - __main__ -     Batch size = 32
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
03/24/2025 07:25:32 - INFO - __main__ -     eval_loss = 0.3819
03/24/2025 07:25:32 - INFO - __main__ -     eval_acc = 0.9087
03/24/2025 07:25:32 - INFO - __main__ -     eval_precision = 0.8371
03/24/2025 07:25:32 - INFO - __main__ -     eval_recall = 0.7996
03/24/2025 07:25:32 - INFO - __main__ -     eval_f1 = 0.8179
03/24/2025 07:25:32 - INFO - __main__ -     eval_auc = 0.8728
03/24/2025 07:25:32 - INFO - __main__ -     eval_FPR = 0.054
03/24/2025 07:25:32 - INFO - __main__ -     eval_FNR = 0.2015
03/24/2025 07:25:33 - INFO - __main__ -   epoch 195 loss 0.07061
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
03/24/2025 08:01:51 - INFO - __main__ -   ***** Running evaluation *****
03/24/2025 08:01:51 - INFO - __main__ -     Num examples = 3657
03/24/2025 08:01:51 - INFO - __main__ -     Batch size = 32
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
03/24/2025 08:06:26 - INFO - __main__ -     eval_loss = 0.3813
03/24/2025 08:06:26 - INFO - __main__ -     eval_acc = 0.9089
03/24/2025 08:06:26 - INFO - __main__ -     eval_precision = 0.8403
03/24/2025 08:06:26 - INFO - __main__ -     eval_recall = 0.7964
03/24/2025 08:06:26 - INFO - __main__ -     eval_f1 = 0.8177
03/24/2025 08:06:26 - INFO - __main__ -     eval_auc = 0.872
03/24/2025 08:06:26 - INFO - __main__ -     eval_FPR = 0.0526
03/24/2025 08:06:26 - INFO - __main__ -     eval_FNR = 0.2047
03/24/2025 08:06:26 - INFO - __main__ -   epoch 196 loss 0.07201
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
03/24/2025 08:42:48 - INFO - __main__ -   ***** Running evaluation *****
03/24/2025 08:42:48 - INFO - __main__ -     Num examples = 3657
03/24/2025 08:42:48 - INFO - __main__ -     Batch size = 32
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
03/24/2025 08:47:25 - INFO - __main__ -     eval_loss = 0.3814
03/24/2025 08:47:25 - INFO - __main__ -     eval_acc = 0.9089
03/24/2025 08:47:25 - INFO - __main__ -     eval_precision = 0.8403
03/24/2025 08:47:25 - INFO - __main__ -     eval_recall = 0.7964
03/24/2025 08:47:25 - INFO - __main__ -     eval_f1 = 0.8177
03/24/2025 08:47:25 - INFO - __main__ -     eval_auc = 0.872
03/24/2025 08:47:25 - INFO - __main__ -     eval_FPR = 0.0526
03/24/2025 08:47:25 - INFO - __main__ -     eval_FNR = 0.2047
03/24/2025 08:47:25 - INFO - __main__ -   epoch 197 loss 0.07044
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
03/24/2025 09:24:04 - INFO - __main__ -   ***** Running evaluation *****
03/24/2025 09:24:04 - INFO - __main__ -     Num examples = 3657
03/24/2025 09:24:04 - INFO - __main__ -     Batch size = 32
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
03/24/2025 09:28:39 - INFO - __main__ -     eval_loss = 0.3811
03/24/2025 09:28:39 - INFO - __main__ -     eval_acc = 0.9089
03/24/2025 09:28:39 - INFO - __main__ -     eval_precision = 0.841
03/24/2025 09:28:39 - INFO - __main__ -     eval_recall = 0.7953
03/24/2025 09:28:39 - INFO - __main__ -     eval_f1 = 0.8175
03/24/2025 09:28:39 - INFO - __main__ -     eval_auc = 0.8716
03/24/2025 09:28:39 - INFO - __main__ -     eval_FPR = 0.0522
03/24/2025 09:28:39 - INFO - __main__ -     eval_FNR = 0.2058
03/24/2025 09:28:40 - INFO - __main__ -   epoch 198 loss 0.07153
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
03/24/2025 10:05:32 - INFO - __main__ -   ***** Running evaluation *****
03/24/2025 10:05:32 - INFO - __main__ -     Num examples = 3657
03/24/2025 10:05:32 - INFO - __main__ -     Batch size = 32
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
03/24/2025 10:10:13 - INFO - __main__ -     eval_loss = 0.3812
03/24/2025 10:10:13 - INFO - __main__ -     eval_acc = 0.9089
03/24/2025 10:10:13 - INFO - __main__ -     eval_precision = 0.841
03/24/2025 10:10:13 - INFO - __main__ -     eval_recall = 0.7953
03/24/2025 10:10:13 - INFO - __main__ -     eval_f1 = 0.8175
03/24/2025 10:10:13 - INFO - __main__ -     eval_auc = 0.8716
03/24/2025 10:10:13 - INFO - __main__ -     eval_FPR = 0.0522
03/24/2025 10:10:13 - INFO - __main__ -     eval_FNR = 0.2058
03/24/2025 10:10:13 - INFO - __main__ -   epoch 199 loss 0.07106
03/24/2025 10:10:14 - INFO - __main__ -   ***** Running evaluation *****
03/24/2025 10:10:14 - INFO - __main__ -     Num examples = 3657
03/24/2025 10:10:14 - INFO - __main__ -     Batch size = 32
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
[W pthreadpool-cpp.cc:90] Warning: Leaking Caffe2 thread-pool after fork. (function pthreadpool)
03/24/2025 10:14:56 - INFO - __main__ -   ***** Eval results *****
03/24/2025 10:14:56 - INFO - __main__ -     eval_FNR = 0.2143
03/24/2025 10:14:56 - INFO - __main__ -     eval_FPR = 0.0445
03/24/2025 10:14:56 - INFO - __main__ -     eval_acc = 0.9125
03/24/2025 10:14:56 - INFO - __main__ -     eval_auc = 0.8712
03/24/2025 10:14:56 - INFO - __main__ -     eval_f1 = 0.8218
03/24/2025 10:14:56 - INFO - __main__ -     eval_loss = 0.3796
03/24/2025 10:14:56 - INFO - __main__ -     eval_precision = 0.8601
03/24/2025 10:14:56 - INFO - __main__ -     eval_recall = 0.7868
03/24/2025 10:14:57 - INFO - __main__ -   ***** Running Test *****
03/24/2025 10:14:57 - INFO - __main__ -     Num examples = 3658
03/24/2025 10:14:57 - INFO - __main__ -     Batch size = 32
03/24/2025 10:27:00 - INFO - __main__ -   ***** Test results *****
03/24/2025 10:27:00 - INFO - __main__ -     test_FNR = 0.2015
03/24/2025 10:27:00 - INFO - __main__ -     test_FPR = 0.0508
03/24/2025 10:27:00 - INFO - __main__ -     test_acc = 0.9101
03/24/2025 10:27:00 - INFO - __main__ -     test_auc = 0.8745
03/24/2025 10:27:00 - INFO - __main__ -     test_f1 = 0.824
03/24/2025 10:27:00 - INFO - __main__ -     test_precision = 0.8499
03/24/2025 10:27:00 - INFO - __main__ -     test_recall = 0.7996
